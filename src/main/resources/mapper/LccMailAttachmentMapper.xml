<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.llq.j5.mapper.LccMailAttachmentMapper">

    <resultMap id="BaseResultMap" type="com.llq.j5.entity.LccMailAttachment">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fileName" column="fileName" jdbcType="VARCHAR"/>
            <result property="fileSize" column="fileSize" jdbcType="BIGINT"/>
            <result property="fileUrl" column="fileUrl" jdbcType="VARCHAR"/>
            <result property="mailId" column="mailId" jdbcType="BIGINT"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,fileName,fileSize,
        fileUrl,mailId,createTime
    </sql>
</mapper>
