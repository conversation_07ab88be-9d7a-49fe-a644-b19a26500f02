<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.llq.j5.mapper.LccMailEntityMapper">

    <resultMap id="BaseResultMap" type="com.llq.j5.entity.LccMailEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="airline" column="airline" jdbcType="VARCHAR"/>
            <result property="recipient" column="recipient" jdbcType="VARCHAR"/>
            <result property="subject" column="subject" jdbcType="VARCHAR"/>
            <result property="receiveDate" column="receiveDate" jdbcType="TIMESTAMP"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="msgId" column="msgId" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="logId" column="logId" jdbcType="BIGINT"/>
            <result property="logType" column="logType" jdbcType="INTEGER"/>
            <result property="createByOrgId" column="createByOrgId" jdbcType="INTEGER"/>
            <result property="createByMemberId" column="createByMemberId" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="tenantOrgId" column="tenantOrgId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,airline,recipient,
        subject,receiveDate,content,
        msgId,desc,status,
        logId,logType,createByOrgId,
        createByMemberId,createTime,tenantOrgId
    </sql>

    <!-- 获取最大 -->
    <select id="selectMaxReceiveDate" resultType="date">
        SELECT MAX(receiveDate) as receiveDate FROM lcc_mail WHERE airline = #{code}
    </select>


</mapper>
