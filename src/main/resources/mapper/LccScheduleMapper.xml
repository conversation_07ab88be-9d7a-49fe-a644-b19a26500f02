<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.llq.j5.mapper.LccScheduleMapper">

    <resultMap id="BaseResultMap" type="com.llq.j5.entity.LccSchedule">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="airline" column="airline" jdbcType="VARCHAR"/>
            <result property="startTime" column="startTime" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="endTime" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="costTime" column="costTime" jdbcType="INTEGER"/>
            <result property="logId" column="logId" jdbcType="BIGINT"/>
            <result property="logType" column="logType" jdbcType="INTEGER"/>
            <result property="createByOrgId" column="createByOrgId" jdbcType="INTEGER"/>
            <result property="createByMemberId" column="createByMemberId" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="tenantOrgId" column="tenantOrgId" jdbcType="INTEGER"/>
            <result property="taskCount" column="taskCount" jdbcType="INTEGER"/>
            <result property="rate" column="rate" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,airline,startTime,
        endTime,status,costTime,
        logId,logType,createByOrgId,
        createByMemberId,createTime,tenantOrgId,
        taskCount,rate
    </sql>
</mapper>
