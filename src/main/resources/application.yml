server:
  port: 8585
  servlet:
    context-path: /5j
  tomcat:
    uri-encoding: UTF-8

spring:
  profiles:
    active: dev
    #active: prod
  application:
    name: crawler_5j

mybatis-plus:
  #mapper配置文件。可能有多个，所以Mapper.xml结尾的都添加进来
  mapper-locations: classpath*:/mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: off
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      update-strategy: not_null  #更新策略，只更新非空字段

# LCC RUN TOKEN
lcc:
  web:
    token: b231749f3dba463d889c29db8a8f1a41
  j5:
    url:
    token:
    connectTimeout: 10000
    readTimeout: 15000


