package com.llq.j5.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * LCC构造政策时传入的对象
 * 构造往返政策时，dateStr_back和lcc_hangDuanList_back必填
 */
@Data
public class Lcc_ZhengCe implements Serializable {
    /**
     * 政策所属航司二字码
     */
    private String hangSi;

    /**
     * 爬取的产品类型，1、S7无行李产品，2、S7有行李产品，3、。。。
     */
    private Integer productType;

    /**
     * 原始币种三字码，爬虫采用的价格币种
     */
    private String originalCurrency;

    /**
     * 报价转换时的汇率值
     */
    private Double exchangeRate;

    /**
     * 政策出发城市三字码
     */
    private String depCity;

    /**
     * 政策目的城市三字码
     */
    private String arrCity;

    /**
     * 库存座位数，实时库存
     */
    private int seatCount;

    /**
     * 去程日期 yyyy-MM-dd,投的都是单日期政策
     */
    private String dateStr_go;

    /**
     * 回程日期 yyyy-MM-dd,投的都是单日期政策
     * 构造往返政策时必传，单程不能传值，需要根据它来判断是否为往返政策
     */
    private String dateStr_back;

    /**
     * 基准底价
     */
    private Double basePrice;

    /**
     * 税费
     */
    private Double taxFeeAount;

    /**
     * 行李额件数，单位PC，枚举值如下 0：表示无免费托运行李， -1：表示行李采用计重制， n（n>0）：表示行李采用计件制，每人可携带n件行李
     */
    private Integer xingLiPiece;

    /**
     * 行李额重量，单位KG，跟xingLiPiece配合使用，无免费行李时，填-1
     */
    private Integer xingLiWeight;

    /**
     * 封装政策的去程航段信息，航段必须严格按照起飞时间先后顺序放入list中
     */
    private List<Lcc_ZhengCe_HangDuan> lcc_hangDuanList_go;

    /**
     * 封装政策的回程航段信息，航段必须严格按照起飞时间先后顺序放入list中
     * 构造往返政策时必传，单程不能传值，需要根据它来判断是否为往返政策
     */
    private List<Lcc_ZhengCe_HangDuan> lcc_hangDuanList_back;

    /**
     * 创建组织ID
     * 将政策放到旅事通的哪个组织下
     * S7的政策传127
     */
    private Integer createByOrgId;

    /**
     * 创建者会员ID
     * 将政策放到旅事通的哪个会员名义下
     * S7的政策传141
     */
    private Integer createByMemberId;

    /**
     * 政策创建时间，传爬虫的爬取时间
     */
    private Date createTime;

    /**
     * 客票全部未使用时不可改期，1不可改期，0可以改期，如果不可改期，没必要传改期费了
     */
    private Integer noChange=1;

    /**
     * 客票全部未使用时起飞前几小时可改期，默认0
     */
    private Integer changeHour=0;

    /**
     * 客票全部未使用时的改期费
     */
    private Double changeFee;

    /**
     * 客票全部未使用时不可推票，1不可退票，0可以退票，如果不可退票，没必要传退票费了
     */
    private Integer noRefund=1;

    /**
     * 客票全部未使用时起飞前几小时可退票，默认0
     */
    private Integer refundHour=0;

    /**
     * 客票全部未使用时的退票费
     */
    private Double refundFee;



    /**
     * 客票返程未使用时不可改期，1不可改期，0可以改期，如果不可改期，没必要传改期费了
     * 往返政策才用到
     */
    private Integer noChange2=1;

    /**
     * 客票返程未使用时起飞前几小时可改期，默认0
     * 往返政策才用到
     */
    private Integer changeHour2=0;

    /**
     * 客票返程未使用时的改期费
     * 往返政策才用到
     */
    private Double changeFee2;

    /**
     * 客票返程未使用时不可推票，1不可退票，0可以退票，如果不可退票，没必要传退票费了
     * 往返政策才用到
     */
    private Integer noRefund2=1;

    /**
     * 客票返程未使用时起飞前几小时可退票，默认0
     * 往返政策才用到
     */
    private Integer refundHour2=0;

    /**
     * 客票返程未使用时的退票费
     * 往返政策才用到
     */
    private Double refundFee2;






}