package com.llq.j5.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统总操作日志表
 * @TableName sys_log
 */
@TableName(value ="sys_log")
@Data
public class Sys_Log implements Serializable {
    /**
     * 系统总操作日志ID
     */
    @TableId(value = "logId", type = IdType.AUTO)
    private Long logId;

    /**
     * 操作人
     */
    @TableField(value = "logMemberId")
    private Integer logMemberId;

    /**
     * 操作时间
     */
    @TableField(value = "logTime")
    private Date logTime;

    /**
     * 操作模块
     */
    @TableField(value = "logModuleId")
    private Integer logModuleId;

    /**
     * 操作表，多个表名之间用英文逗号隔开
     */
    @TableField(value = "logTableNames")
    private String logTableNames;

}