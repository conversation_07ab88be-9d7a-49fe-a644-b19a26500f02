package com.llq.j5.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName lcc_mail
 */
@TableName(value ="lcc_mail")
@Data
public class LccMailEntity implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 航司
     */
    @TableField(value = "airline")
    private String airline;

    /**
     * 收件人
     */
    @TableField(value = "recipient")
    private String recipient;

    /**
     * 主题
     */
    @TableField(value = "subject")
    private String subject;

    /**
     * 发件时间
     */
    @TableField(value = "sendDate")
    private Date sendDate;


    /**
     * 收件时间
     */
    @TableField(value = "receiveDate")
    private Date receiveDate;

    /**
     * 
     */
    @TableField(value = "content")
    private String content;

    /**
     * 消息ID
     */
    @TableField(value = "msgId")
    private String msgId;

    /**
     * 描述
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 状态
     */
    @TableField(value = "status")
    private String status;

    /**
     * 邮件类型
     */
    @TableField(value = "mailType")
    private String mailType;


    /**
     * 日志ID
     */
    @TableField(value = "logId")
    private Long logId;

    /**
     * 日志类型,1为添加，0为修改，-1为删除
     */
    @TableField(value = "logType")
    private Integer logType;

    /**
     * 文件服务地址
     *
     * http://60.205.107.111:8787/s7
     */
    @TableField(value = "server")
    private String server;


    /**
     * 创建组织ID
     */
    @TableField(value = "createByOrgId")
    private Integer createByOrgId;

    /**
     * 创建者会员ID
     */
    @TableField(value = "createByMemberId")
    private Integer createByMemberId;

    /**
     * 创建时间，修改时也更新
     */
    @TableField(value = "createTime")
    private Date createTime;

    /**
     * 创建时所属租户组织ID
     */
    @TableField(value = "tenantOrgId")
    private Integer tenantOrgId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}