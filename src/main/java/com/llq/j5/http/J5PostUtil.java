package com.llq.j5.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.llq.j5.dto.LccResultDataDto;
import com.llq.j5.vo.J5ApiResultVo;
import com.llq.j5.vo.Routing;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/07/09
 **/
@Component
@Slf4j
public class J5PostUtil {


    @Resource
    private RestTemplate restTemplate;



    /**
     * 接口请求
     *
     * @param json
     * @return
     */
    public J5ApiResultVo post(String json) {
        //return this.publicPost(url, json);
        int loop = 10;
        while (loop > 0){
            if ( ApiRateLimiter.tryAccess()){
                return this.privatePost(json);
            }else {
                try{Thread.sleep(100); }catch (Exception e){
                    e.printStackTrace();
                }
            }
            loop--;
        }
        return J5ApiResultVo.fail("尝试失败");
    }

    /**
     * 接口请求
     *
     * @param json
     * @return
     */
    public J5ApiResultVo publicPost(String url, String json) {
        //log.info("J5PostUtil-post-url:{},param:{}" ,url,json);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(json, headers);
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
            if (HttpStatus.OK.equals(response.getStatusCode())) {
                String retJson = response.getBody();
                JSONObject retJsonObject = JSON.parseObject(retJson);
                int code = retJsonObject.getIntValue("code");
                if (code == 200) {
                    JSONArray retDataJsonArray = retJsonObject.getJSONArray("data");
                    List<Routing> j5DataVo = retDataJsonArray.toJavaList(Routing.class);
                    return J5ApiResultVo.ok(j5DataVo);
                }
                String msg = retJsonObject.getString("data");
                return J5ApiResultVo.fail(msg);
            }
            return J5ApiResultVo.fail(response.toString());
        } catch (Exception e) {
            log.error("J5PostUtil-post-ex:" + e.getMessage());
            return J5ApiResultVo.fail(e.getMessage());
        }
    }


    /**
     *
     * 自研调用
     * @param json
     * @return
     */
    public J5ApiResultVo privatePost(String json) {
        //log.info("J5PostUtil-post-url:{},param:{}" ,url,json);
        try {
           String ret =  new J5Task().wrapperRequest(json,"search");
           if (Strings.isNullOrEmpty(ret) || ret.contains("ERROR_")){
               return J5ApiResultVo.fail(ret);
           }else {
               JSONObject retJsonObject = JSON.parseObject(ret);
               int code = retJsonObject.getIntValue("code");
               if (code == 200) {
                   JSONArray retDataJsonArray = retJsonObject.getJSONArray("data");
                   List<Routing> j5DataVo = retDataJsonArray.toJavaList(Routing.class);
                   return J5ApiResultVo.ok(j5DataVo);
               }
               String msg = retJsonObject.getString("data");
               return J5ApiResultVo.fail(msg);
           }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("J5PostUtil-post-ex:" + e.getMessage());
            return J5ApiResultVo.fail(e.getMessage());
        }
    }
}