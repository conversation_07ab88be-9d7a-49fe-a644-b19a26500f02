package com.llq.j5.http;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.llq.j5.dto.J5DataConDto;
import com.llq.j5.dto.J5DataParamPassengerDto;
import com.llq.j5.util.CommonUtil;
import com.llq.j5.vo.Flight;
import com.llq.j5.vo.Passenger;
import com.llq.j5.vo.Routing;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.Credentials;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.conn.params.ConnRoutePNames;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.lang3.math.NumberUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;

import static com.llq.j5.http.J5HttpUtil.readHtmlContentFromEntity;

/**
 * 张博
 */
@Slf4j
public class J5Task {

    private static Logger logger = LoggerFactory.getLogger(J5Task.class);

    static Invocable invoke = null;

    static {
        try {
            ScriptEngineManager mgr = new ScriptEngineManager();
            ScriptEngine engine = mgr.getEngineByName("javascript");
            invoke = (Invocable) engine;

            // 从 classpath 加载 js 文件
            try (InputStream jsStream =
                         J5Task.class.getClassLoader().getResourceAsStream("aes.js")) {
                if (jsStream == null) {
                    throw new IllegalStateException("找不到加密脚本文件: aes.js");
                }

                // 读取 js 文件内容
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(jsStream, StandardCharsets.UTF_8))) {
                    StringBuilder jsContent = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        jsContent.append(line).append("\n");
                    }
                    engine.eval(jsContent.toString());
                    logger.info("已加载加密脚本");
                }
            }
        } catch (Exception e) {
            logger.info("加载加密脚本失败", e);
            throw new RuntimeException("初始化加密脚本失败", e);
        }
    }

    private static String OCP_APIM_SUBSCRIPTION_KEY = "07ce68d9937841a59a8156ec7dafc0b6";
    int uas = 131;
    String USER_AGENT = "Cebu Pacific/3.69.0 (com.navitaire.nps.5j; build:1; iOS 16.3.1)";
    public static String AUTHORIZATION_SECRET =
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcHBuYW1lIjoiY2VidXBhY2lmaWMiLCJ2ZXJzaW9uIjoiMi42NC4wIiwicGxhdGZvcm0iOiJBTkRST0lEIiwiTlNUb2tlbiI6bnVsbCwidXJsIjoiaHR0cHM6Ly9tb2JpbGUtYXBpLmNlYnVwYWNpZmljYWlyLmNvbS9kb3RyZXotcHJvZC12My9hcGkvbnNrL3YxL1Rva2VuIn0.MYcI-hE5Mwu-rK3jGwQPUbiD2jB3ykaGL_XIbyrrfno";
    // 保留小数点后两位
    DecimalFormat df = new DecimalFormat("#.##");
    public static List<String> luanges = Lists.newArrayList();

    static {
        luanges.add("en;q=0.8");
        luanges.add("en-GB;q=0.7");
        luanges.add("en-US;q=0.6");
        luanges.add("de;q=0.5");
        luanges.add("ja;q=0.4");
    }

    private String currence = "";
    protected final static int CONNECTION_TIMEOUT = 8000;


    private static LinkedBlockingQueue<String> locklist = new LinkedBlockingQueue<String>();

    private static AtomicInteger requestCount = new AtomicInteger(0);
    private static AtomicInteger addonCount = new AtomicInteger(0);
    private static AtomicInteger seatCount = new AtomicInteger(0);
    private static AtomicInteger baggageCount = new AtomicInteger(0);
    private static AtomicInteger failCount = new AtomicInteger(0);

    private static AtomicInteger runCount = new AtomicInteger(0);

    private boolean hasAdministrativeFee = true;


    static int search_count_ceb = 0;
    static int search_count_php = 0;
    static int search_count_other = 0;

    String stage = "search";
    String proxy_str = "";
    String uniqueid = "";
    String token = "";
    String aes_auth = "";
    String total_token = "";
    String searchContent = "";

    public static String PARK = "VwxG&vJSrS-3*?7z";
    public static String AESK = "d15be270a756e84bae09ea88b12e80af6596541e6a5266f73a6980f1669bd718";
    public static String XAT = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb2RlVmVyc2lvbiI6Imh2dzlZcnBPTWRmenJ5RkZKZ1I5SHo0Umo3NnFoM25FIn0.FQhLXkyaetq7RZb4wIoTTdGFcHFiopsxvRsZpRlEtiU";
    public static String SECR = "kC2ghRh4XthMdfdY";

    String flightData = "";

    String wrapper = "";
    int adult = 1;
    int child = 0;
    int infant = 0;

    String currency_code = "";

    String search = "";
    String select = "";


    String proxy_host = "";
    int proxy_post = 0;
    String proxy_user = "";
    String proxy_pass = "";


    /**
     * 查询入口
     *
     * @param requestInfo
     * @param stage
     * @return
     */
    public String wrapperRequest(String requestInfo, String stage) {

        USER_AGENT = "Cebu Pacific/3.73.1 (com.navitaire.nps.5j; build:1; iOS " + (random.nextInt(100) + 10) + "." + random.nextInt(10) + "." + random.nextInt(10) + ")";

        J5DataConDto info = JSONObject.parseObject(requestInfo, J5DataConDto.class);


        String fromCity = info.getDepCity();
        String toCity = info.getArrCity();
        String fromDate = info.getDepartureDate().replaceAll("-", "");
        // 币种
        currency_code = info.getCurrency();


        J5DataParamPassengerDto passenger = info.getPassengerDto();
        adult = passenger.getAdults();

        DefaultHttpClient client = J5HttpUtil.getHttpClient("127.0.0.1:8888");


        String getProxy = QueueUtil.poll();
        if (Strings.isNullOrEmpty(getProxy)) {
            return "ERROR_GET_IP_NULL";
        }

        String[] split_proxy = getProxy.split(":");

        proxy_host = split_proxy[0];
        proxy_post = Integer.parseInt(split_proxy[1].replaceAll("\r", "").replaceAll("\n", ""));
//        proxy_user = split[2];
//        proxy_pass = split[3];
        proxy_user = "luluqi";
        proxy_pass = "luluqi";

//        proxy_ip(client);

        String retDate = "";
        List<Routing> routings = new ArrayList<>();

        uas = random.nextInt(13) + 132;

        String unique_id = IdUtil.fastSimpleUUID();

        try {

            Map<String, List<Flight>> goMap = new HashMap<>();

            List<Map<String, List<Flight>>> result = Lists.newArrayList();
            try {
                int n = 3;
                do {
                    result = mobileSeach(fromCity, toCity, fromDate, retDate, unique_id, client);
                    if (result.size() > 0) {
                        break;
                    }
                    Thread.sleep(new Random().nextInt(1000));
                } while (--n > 0);
            } catch (Exception e) {
                return "ERROR_SEARCH_EX:" + e.getMessage();
            }

            if (result.size() > 0) {
                goMap = result.get(0);
            }

            if ("search".equals(stage) && goMap.size() == 0) {
                //空数据直接返回
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("code", 200);
                jsonObject1.put("data", Lists.newArrayList());
                return jsonObject1.toJSONString();
            }

            Iterator<String> goKey = goMap.keySet().iterator();

            while (goKey.hasNext()) {
                Routing goR = new Routing();
                String go = goKey.next();
                // 0 1 2 3 4
                // i_pp_10_available_&flightKey&fareKey&currencyCode&price
                String goMoney[] = go.split("_");
                if ((int) Double.parseDouble(goMoney[1]) == 0) {
                    continue;
                }
                goR.setFromSegments(goMap.get(go));

                // 去程的单人价格和税
                goR.setAdultPrice(Double.parseDouble(goMoney[1]));
                goR.setAdultTax(Double.parseDouble(goMoney[2]));
                // 去程座位数
                goR.getExtraInfo().put("seat", goMoney[3]);

                String split[] = go.split("&");

                // set币种
                goR.setCurrency(split[3]);

                // 去程原价
                goR.getExtraInfo().put("srcPrice", df.format(Double.parseDouble(split[4])));

                // 去程flight key
                goR.getExtraInfo().put("flight_key", split[1]);
                goR.getExtraInfo().put("fare_key", split[2]);
                goR.getExtraInfo().put("segmentKey", split[split.length - 1]);

                // 单程情况下

                goR.setData(CommonUtil.flightToKey(goR));
                goR.setAdultTaxType(0);
                goR.setApplyType(0);
                goR.getExtraInfo().put("wrapper", "cebupacifi");
                goR.getExtraInfo().put("currencyCode", split[3]);
                goR.getExtraInfo().put("srcCur", split[3]);

                // set max
                int seat = NumberUtils.toInt(goMoney[3]);
                if (seat > 0) {
                    if (seat > 9) {
                        seat = 9;
                    }
                    goR.setMax(seat + "");
                }

                if (NumberUtils.toInt(goR.getMax()) > 9) {
                    goR.setMax("9");
                }
                routings.add(goR);

            }

            /**
             *
             * 成功返回
             */
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("code", 200);
            jsonObject1.put("data", routings);
            return jsonObject1.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "ERROR_wrapperRequest_EX: " + e.getMessage();
        }
    }


    public String getSeatMap(String ow_key, String rt_key, String token, String aes_auth,
                             String segmentKey) {
        String ow = ow_key.split("&")[0];
        JSONArray jsonArray = new JSONArray();

        String[] split = segmentKey.split("#");
        for (int i = 0; i < split.length; i++) {
            jsonArray.add(split[i]);
        }
        if (StringUtils.isNotBlank(rt_key)) {
            String rt = rt_key.split("&")[0];
            jsonArray.add(rt);
        }
        // logger.info("本次获取航班座位分布参数:{}" + jsonArray.toString());
        String action = "seatmaps";
        // String req_url = "https://soar.cebupacificair.com/ceb-omnix_proxy?content=";
        // String seat_map = sendsslpostParam(client, req_url, action, jsonArray.toString(),
        // "获取所有座位分布", order_no);
        String time = System.currentTimeMillis() + "";
        Map<String, String> authMap = sendPostSO(action, jsonArray.toString(), token, aes_auth, "查询座位", uniqueid, J5HttpUtil.getHttpClient(""));
        String content = authMap.get("content");
        return content;
    }


    /**
     * 搜索行李价格
     *
     * @param routing
     */

    public List<Object[]> getBagInfo(Routing routing, String fromDate, String token, String aes_auth, DefaultHttpClient client) {

        try {
            addonCount.incrementAndGet();

            // 去数据库查询是否有行李数据
            List<Flight> fromSegments = routing.getFromSegments();
            String dep = fromSegments.get(0).getDepAirport();
            String arr = fromSegments.get(fromSegments.size() - 1).getArrAirport();
            String carrier = fromSegments.get(0).getCarrier().toLowerCase();


            String go_flight_key = routing.getExtraInfo().containsKey("flight_key")
                    ? routing.getExtraInfo().get("flight_key")
                    : routing.getExtraInfo().get("go_flight_key");
            String go_fare_key = routing.getExtraInfo().containsKey("fare_key")
                    ? routing.getExtraInfo().get("fare_key")
                    : routing.getExtraInfo().get("go_fare_key");
            String currencyCode = routing.getExtraInfo().get("currencyCode");
            String segmentKey = routing.getExtraInfo().get("segmentKey");
            String ow_key = go_flight_key + "&" + go_fare_key + "&" + 0 + "&" + routing.getMax()
                    + "&" + currencyCode;
            String re_flight_key = routing.getExtraInfo().get("re_flight_key");
            String re_fare_key = routing.getExtraInfo().get("re_fare_key");
            String rt_key =
                    (StringUtils.isBlank(re_fare_key) || StringUtils.isBlank(re_flight_key)) ? ""
                            : re_flight_key + "&" + re_fare_key + "&" + 0 + "&" + routing.getMax()
                            + "&" + currencyCode;

            List<String> keys = Lists.newArrayList();
            keys.add(ow_key);
            if (StringUtils.isNotBlank(rt_key)) {
                keys.add(rt_key);
            }

            String select_count = selectFlights(keys, 1, 0, currencyCode, client);

            select = select_count;
            JSONObject jsonObject = JSONObject.parseObject(select_count);
            JSONArray baggage =
                    jsonObject.getJSONObject("availableSsrs").getJSONArray("journeySsrs");
            JSONArray goSsrs = baggage.getJSONObject(0).getJSONArray("ssrs");
            JSONArray reSsrs =
                    baggage.size() > 1 ? baggage.getJSONObject(1).getJSONArray("ssrs") : null;

            String depDate = "";
            String flightNo = "";
            // 去程行李update
            List<Object[]> objects = updateBaggage(carrier, dep, arr, depDate, flightNo, currencyCode, goSsrs);
//            baggageCount.incrementAndGet();
//
//            // 回程行李update
//            if (reSsrs != null) {
//                updateBaggage(carrier, arr, dep, depDate, flightNo, currencyCode, reSsrs);
//            }
//
//            String seatMap = getSeatMap(ow_key, rt_key, token, aes_auth, segmentKey);
//            seatInfo(seatMap, routing, currencyCode, fromDate);
//            seatCount.incrementAndGet();
            return objects;
        } catch (Exception e) {
            failCount.incrementAndGet();
            logger.info("航线>>" + routing.getData() + ">>抓取行李价格异常>>" + e.getMessage());
            e.printStackTrace();
        } finally {
            logger.info("book的统计计数：requestCount=" + requestCount.get() + ", addonCount="
                    + addonCount.get() + ", seatCount=" + seatCount.get() + ", baggageCount="
                    + baggageCount.get() + ", failCount=" + failCount.get());
        }
        return null;
    }

    private void seatInfo(String seatMap, Routing routing, String currencyCode, String fromDate) {
        JSONArray flightList = JSONArray.parseArray(seatMap);
        JSONObject flight = new JSONObject();
        for (int i = 0; i < flightList.size(); i++) {
            JSONObject jsonObject = new JSONObject();
            List<Flight> fromSegments = routing.getFromSegments();

            JSONObject seatJSON = flightList.getJSONObject(i);
            String departureStation = seatJSON.getString("departureStation");
            String arrivalStation = seatJSON.getString("arrivalStation");

            // 获取当前出发到达的航班号
            String flightNumber = "";
            for (Flight fromSegment : fromSegments) {
                String depAirport = fromSegment.getDepAirport();
                String arrAirport = fromSegment.getArrAirport();
                if (depAirport.equals(departureStation) && arrAirport.equals(arrivalStation)) {
                    flightNumber = fromSegment.getFlightNumber();
                }
            }


            String planeType = seatJSON.getString("name").split(" ")[0];
            jsonObject.put("planeType", planeType);
            JSONArray units = seatJSON.getJSONArray("units");
            JSONArray availableSeats = new JSONArray();
            JSONArray unavailableSeats = new JSONArray();
            JSONArray unavailableSeatInfo = new JSONArray();
            JSONArray groupTypes = new JSONArray();

            // group价格整理
            JSONObject groupJSON = new JSONObject();
            int count = 1;
            for (int j = 0; j < units.size(); j++) {
                JSONObject group = new JSONObject();
                JSONObject availableSeat = new JSONObject();
                JSONObject unit = units.getJSONObject(j);
                String designator = unit.getString("designator");
                String properties = unit.getJSONArray("properties").toString();
                String availability = unit.getString("availability");
                String amount = unit.getString("amount");
                if (availability.equals("Open")) {
                    availableSeat.put("aisle", properties.contains("AISLE"));
                    availableSeat.put("childAllowed", true);
                    availableSeat.put("designator", designator);
                    availableSeat.put("emergencyExit", properties.contains("EXITROW"));
                    availableSeat.put("extraLegRoom", properties.contains("LEGROOM"));
                    if (!groupJSON.containsKey(amount)) {
                        availableSeat.put("group", count + "");
                        group.put("srcCurrency", currencyCode);
                        group.put("group", count + "");
                        group.put("srcPrice", amount);
                        groupTypes.add(group);
                        groupJSON.put(amount, count + "");
                        count++;
                    } else {
                        String groupCount = groupJSON.getString(amount);
                        availableSeat.put("group", groupCount);
                    }
                    availableSeat.put("infantAllowed", "");
                    availableSeat.put("upFront", "false");
                    availableSeat.put("windowSeat", properties.contains("WINDOW"));
                    availableSeats.add(availableSeat);
                } else {
                    availableSeat.put("aisle", properties.contains("AISLE"));
                    availableSeat.put("childAllowed", "");
                    availableSeat.put("designator", designator);
                    availableSeat.put("emergencyExit", properties.contains("EXITROW"));
                    availableSeat.put("extraLegRoom", properties.contains("LEGROOM"));
                    availableSeat.put("group", "");
                    availableSeat.put("infantAllowed", true);
                    availableSeat.put("upFront", "false");
                    availableSeat.put("windowSeat", properties.contains("WINDOW"));
                    unavailableSeatInfo.add(availableSeat);
                    unavailableSeats.add(designator);
                }
            }
            jsonObject.put("availableSeat", availableSeats);
            jsonObject.put("groupType", groupTypes);
            jsonObject.put("unavailableSeat", unavailableSeats);
            jsonObject.put("unavailableSeatInfo", unavailableSeatInfo);
            flight.put(flightNumber, jsonObject);
        }
        // DBHelper.getInstance().updateSql("replace into bk_seat_new
        // (fromCity,toCity,depDate,flightNo,info,updateTime,returnType) values
        // (?,?,?,?,?,now(),0)", new
        // Object[]{routing.getFromSegments().get(0).getDepAirport(),routing.getFromSegments().get(routing.getFromSegments().size()-1).getArrAirport(),fromDate,routing.getData(),flight.toString()},
        // DBHelper.DEFAULT);
        // log.info("座位JSON"+flight);
    }


    /**
     * 更新航程不同规格的行李价格
     */
    private List<Object[]> updateBaggage(String carrier, String dep, String arr, String depDate,
                                         String flightNo, String currencyCode, JSONArray ssrs) {

        int PC20 = 0;
        int P20B = 0;
        int P20C = 0;
        int PC32 = 0;
        int EX4 = 0;
        int EX12 = 0;

        for (int i = ssrs.size() - 1; i >= 0; i--) {
            JSONObject ssr = (JSONObject) ssrs.get(i);

            if ("PC20".equals(ssr.getString("ssrCode"))
                    || "PC20".equals(ssr.getString("feeCode"))) {
                PC20 = ssr.getJSONArray("passengersAvailability").getJSONObject(0)
                        .getJSONObject("value").getIntValue("price");
            }

            if ("P20B".equals(ssr.getString("ssrCode"))
                    || "P20B".equals(ssr.getString("feeCode"))) {
                P20B = ssr.getJSONArray("passengersAvailability").getJSONObject(0)
                        .getJSONObject("value").getIntValue("price");
            }

            if ("P20C".equals(ssr.getString("ssrCode"))
                    || "P20C".equals(ssr.getString("feeCode"))) {
                P20C = ssr.getJSONArray("passengersAvailability").getJSONObject(0)
                        .getJSONObject("value").getIntValue("price");
            }

            if ("PC32".equals(ssr.getString("ssrCode"))
                    || "PC32".equals(ssr.getString("feeCode"))) {
                PC32 = ssr.getJSONArray("passengersAvailability").getJSONObject(0)
                        .getJSONObject("value").getIntValue("price");
            }

            if ("4EX".equals(ssr.getString("ssrCode")) || "4EX".equals(ssr.getString("feeCode"))) {
                EX4 = ssr.getJSONArray("passengersAvailability").getJSONObject(0)
                        .getJSONObject("value").getIntValue("price");
            }

            if ("12EX".equals(ssr.getString("ssrCode"))
                    || "12EX".equals(ssr.getString("feeCode"))) {
                EX12 = ssr.getJSONArray("passengersAvailability").getJSONObject(0)
                        .getJSONObject("value").getIntValue("price");
            }
        }

        List<Object[]> parameterFor5j = new ArrayList<>();
        List<Object[]> parameterForCebupacifi = new ArrayList<>();
        for (int pc = 1; pc <= 3; pc++) {
            for (int weight = 20; weight <= 32; weight += 4) {
                int price = 0;
                if (pc == 1) {
                    if (weight == 20) {
                        if (PC20 == 0) {
                            continue;
                        }
                        price = PC20;
                    } else if (weight == 24) {
                        if (PC20 == 0 || EX4 == 0) {
                            continue;
                        }
                        price = PC20 + EX4;
                    } else if (weight == 28) {
                        if (PC32 == 0) {
                            continue;
                        }
                        price = PC32;
                    } else {
                        if (PC32 == 0) {
                            continue;
                        }
                        price = PC32;
                    }
                } else if (pc == 2) {
                    if (weight == 20) {
                        if (PC20 == 0 || P20B == 0) {
                            continue;
                        }
                        price = PC20 + P20B;
                    } else if (weight == 24) {
                        if (PC20 == 0 || P20B == 0 || EX4 == 0) {
                            continue;
                        }
                        price = PC20 + P20B + pc * EX4;
                    } else if (weight == 28) {
                        if (PC32 == 0) {
                            continue;
                        }
                        price = pc * PC32;
                    } else {
                        if (PC32 == 0) {
                            continue;
                        }
                        price = pc * PC32;
                    }
                } else if (pc == 3) {
                    if (weight == 20) {
                        if (PC20 == 0 || P20B == 0 || P20C == 0) {
                            continue;
                        }
                        price = PC20 + P20B + P20C;
                    } else if (weight == 32) {
                        if (PC20 == 0 || P20B == 0 || P20C == 0 || EX12 == 0) {
                            continue;
                        }
                        price = PC20 + P20B + P20C + pc * EX12;
                    } else {
                        continue;
                    }
                }

                String outId = "F5j" + dep + arr + pc + (pc * weight);
                Object[] para = new Object[]{outId, "5j", "", dep, arr, "", pc, (pc * weight),
                        price, currencyCode};
                parameterFor5j.add(para);

                String outId2 = "TFcebupacifi" + dep + arr + pc + (pc * weight);
                Object[] para2 = new Object[]{outId2, "cebupacifi", "", dep, arr, "", pc,
                        (pc * weight), price, currencyCode};
                parameterForCebupacifi.add(para2);
                System.out.printf("航线:%s>>%dx%dkg行李入库参数>>%s", dep + "-" + arr, pc, weight,
                        Arrays.toString(para));
                // log.info();
            }
        }
        return parameterFor5j;
        // update batch
        // DBHelper.getInstance().updateBatchSql(
        // "replace into baggage
        // (outId,wrapper,depDate,fromCity,toCity,flightNo,pc,weight,price,cur,returnType) values
        // (?,?,?,?,?,?,?,?,?,?,0) ",
        // parameterFor5j, DBHelper.DEFAULT);
        // DBHelper.getInstance().updateBatchSql(
        // "replace into baggage_new_ctrip
        // (outId,wrapper,depDate,fromCity,toCity,flightNo,pc,weight,price,cur,isFlag) values
        // (?,?,?,?,?,?,?,?,?,?,0) ",
        // parameterForCebupacifi, DBHelper.DEFAULT);
    }

    public String selectFlights(List<String> keys, int adu, int chd, String cur, DefaultHttpClient client) {

        String action = "v3/trip";
        JSONObject paramJson = new JSONObject();
        JSONObject infantCount = new JSONObject();
        infantCount.put("seat", "0");
        infantCount.put("lap", 0);
        paramJson.put("infantCount", infantCount);
        paramJson.put("promoCode", "");
        paramJson.put("adultCount", adu);
        JSONArray jsonArray = new JSONArray();
        if (!currency_code.equals("HKD"))
            jsonArray.add("WAFI");
        paramJson.put("ssrs", jsonArray);
        paramJson.put("childCount", chd);
        paramJson.put("currency", cur);

        JSONArray bundles = new JSONArray();
        JSONObject bundle = new JSONObject();
        bundle.put("journeyKey", keys.get(0).split("&")[0]);
//        bundle.put("bundles", new JSONArray());

        bundles.add(bundle);
        bundle = new JSONObject();
//        if (isBagED) {
//            bundle.put("bundles", edArray);
//        } else {
        bundle.put("bundles", new JSONArray());
//        }
        bundles.add(bundle);

        paramJson.put("bundles", bundles);
        paramJson.put("isRebookEnable", false);

        JSONArray routes = new JSONArray();
        for (int i = 0; i < keys.size(); i++) {
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("fareAvailabilityKey", keys.get(i).split("&")[1]);
            jsonObject1.put("journeyKey", keys.get(i).split("&")[0]);
//            bundle.put("bundles", edArray);
//            if (isBagED) {
//                jsonObject1.put("bundleCode", "EDPC");
//            }
            routes.add(jsonObject1);
        }


        String time = System.currentTimeMillis() + "";
        String hashString = uniqueid + "/" + action + time + aes_auth;

        paramJson.put("routes", routes);
        paramJson.put("hash", generateHMACForSearch(hashString));
        paramJson.put("nonce", time);
        Map<String, String> select_result = sendPostSO(action, paramJson.toString(), token, aes_auth, "select", uniqueid, client);
        // sendsslpostParam(client, req_url, action, jsonObject.toString(), "选择航班", token,
        // aes_auth);
        return select_result.get("content");
    }

    public String addPassenger(List<Passenger> passengers, List<String> pass_keys, String token,
                               String aes_auth, DefaultHttpClient client) {

        String action = "guestdetails";
        String req_url = "https://soar.cebupacificair.com/ceb-omnix_proxy?content=";
        String c_title = "MS";
        if (passengers.get(0).getGender().equals("M")) {
            c_title = "MR";
        }
        String passenger_params = "";
        if (pass_keys.size() != passengers.size()) {
            logger.info("--" + "{}添加乘客失败;乘客key不足");
            return "ERROR_添加乘客失败;乘客key不足";
        }
        for (int i = 0; i < passengers.size(); i++) {
            int ageType = passengers.get(i).getAgeType();
            String pass_key = "";
            if (ageType == 0) {
                for (int i1 = 0; i1 < pass_keys.size(); i1++) {
                    String passs_type = pass_keys.get(i1).split("&")[0];
                    if (passs_type.equals("ADT")) {
                        pass_key = pass_keys.get(i1).split("&")[1];
                        pass_keys.remove(pass_keys.get(i1));
                        break;
                    }

                }
            }
            if (ageType == 1) {
                for (int i1 = 0; i1 < pass_keys.size(); i1++) {
                    String passs_type = pass_keys.get(i1).split("&")[0];
                    if (passs_type.equals("CHD")) {
                        pass_key = pass_keys.get(i1).split("&")[1];
                        pass_keys.remove(pass_keys.get(i1));
                        break;
                    }
                }
            }
            if (StringUtils.isBlank(pass_key)) {
                logger.info("--" + "{}获取的乘客KEY为空;结束");
                return "ERROR_获取的乘客KEY为空;不继续";
            }
            String title = "MS";
            String gender = "Female";
            if (passengers.get(i).getGender().equals("M")) {
                title = "MR";
                gender = "Male";
            }
            String birthday = passengers.get(i).getBirthday();
            birthday = birthday.split("T")[0];
            passenger_params += "{" + "  \"isInfant\" : false," + "  \"ssrs\" : [" + "  ],"
                    + "  \"travelDocuments\" : [" + "  ]," + "  \"passengerKey\" : \"" + pass_key
                    + "\"," + "  \"name\" : {" + "    \"middle\" : \"\"," + "    \"first\" : \""
                    + passengers.get(i).getName().split("/")[1] + "\"," + "    \"last\" : \""
                    + passengers.get(i).getName().split("/")[0] + "\"," + "    \"suffix\" : \"\","
                    + "    \"title\" : \"" + title + "\"" + "  }," + "  \"info\" : {"
                    + "    \"nationality\" : \"" + passengers.get(i).getNationality() + "\","
                    + "    \"residentCountry\" : \"" + passengers.get(i).getNationality() + "\","
                    + "    \"gender\" : \"" + gender + "\"," + "    \"dateOfBirth\" : \""
                    + birthday.substring(0, 4) + "-" + birthday.substring(4, 6) + "-"
                    + birthday.substring(6, 8) + "\"" + "  }" + "}";
            if (i != passengers.size() - 1) {
                passenger_params = passenger_params + ",";
            }
        }
        String phone_num = getPhoneNo();
        String email = phone_num + "@163.com";
        String params = "{" + "  \"contacts\" : [" + "    {" + "      \"contactTypeCode\" : \"P\","
                + "      \"emailAddress\" : \"" + email + "\"," + "      \"name\" : {"
                + "        \"last\" : \"" + passengers.get(0).getName().split("/")[0] + "\","
                + "        \"title\" : \"" + c_title + "\"," + "        \"suffix\" : \"\","
                + "        \"middle\" : \"\"," + "        \"first\" : \""
                + passengers.get(0).getName().split("/")[1] + "\"" + "      },"
                + "      \"phoneNumbers\" : [" + "        {" + "          \"type\" : \"Home\","
                + "          \"number\" : \"" + phone_num + "\"" + "        }," + "        {"
                + "          \"type\" : \"Other\"," + "          \"number\" : \"" + phone_num + "\""
                + "        }" + "      ]" + "    }" + "  ]," + "  \"passengers\" : ["
                + passenger_params + "  ]" + "}";
        Map<String, String> addPass_Result =
                sendPostSO(action, params, token, aes_auth, "add_pass", uniqueid, client);
        // String addpass_count = sendsslpostParam(client, req_url, action, params, "添加乘客", token,
        // aes_auth);
        String addpass_count = addPass_Result.get("content");
        log.info(addpass_count);
        return addpass_count;
    }

    public void addRoundTrip(Routing goR, Routing rtR, List<Routing> routings) {
        Routing routing = new Routing();

        // routine
        routing.setAdultTaxType(0);
        routing.setApplyType(0);

        routing.setFromSegments(goR.getFromSegments());
        routing.setRetSegments(rtR.getRetSegments());

        routing.getExtraInfo().put("wrapper", "cebupacifi");
        routing.getExtraInfo().put("currencyCode", goR.getCurrency());
        routing.getExtraInfo().put("srcCur", goR.getCurrency());
        // 去程原价
        String goSrcPrice = goR.getExtraInfo().get("srcPrice");
        // 回程原价
        String rtSrcPrice = rtR.getExtraInfo().get("srcPrice");
        // 保留后两位相加
        routing.getExtraInfo().put("srcPrice",
                df.format(Double.parseDouble(goSrcPrice) + Double.parseDouble(rtSrcPrice)));

        // 去程flight key
        routing.getExtraInfo().put("go_flight_key", goR.getExtraInfo().get("flight_key"));
        routing.getExtraInfo().put("go_fare_key", goR.getExtraInfo().get("fare_key"));
        // 回程flight key
        routing.getExtraInfo().put("re_flight_key", rtR.getExtraInfo().get("flight_key"));
        routing.getExtraInfo().put("re_fare_key", rtR.getExtraInfo().get("fare_key"));

        // 拼接航班号
        routing.setData(CommonUtil.flightToKey(routing));

        // 单人价格 相加
        routing.setAdultPrice(goR.getAdultPrice() + rtR.getAdultPrice());
        // 单人税 相加
        routing.setAdultTax(goR.getAdultTax() + rtR.getAdultTax());
        // 剩余座位数 取较小值
        String goSeat = goR.getExtraInfo().get("seat");
        String rtSeat = rtR.getExtraInfo().get("seat");
        int seat = Math.min(NumberUtils.toInt(goSeat), NumberUtils.toInt(rtSeat));
        routing.getExtraInfo().put("seat", String.valueOf(seat));

        // set max
        if (seat > 0) {
            if (seat > 7) {
                seat = 7;
            }
            routing.setMax(seat + "");
        }

        if (NumberUtils.toInt(rtR.getMax()) > 4) {
            routing.setMax("4");
        }
        // add
        routings.add(routing);
    }

    public static String getChar(int length) {
        char[] ss = new char[length];
        int i = 0;
        while (i < length) {

            ss[i] = (char) ('A' + Math.random() * 26);

            i++;
        }
        String str = new String(ss);
        return str;
    }

    public static String getPhoneNo() {
        Random random = new Random();
        String[] titles = {"176", "131", "156", "137", "132", "188", "155", "136", "133", "130",
                "132", "185", "186", "151", "152", "189", "175"};
        int index = random.nextInt(titles.length);
        String title = titles[index];

        int one = random.nextInt(10);
        int two = random.nextInt(10);
        int three = random.nextInt(10);
        int forn = random.nextInt(10);
        String middle = one + "" + two + three + forn;

        int one1 = random.nextInt(10);
        int two1 = random.nextInt(10);
        int three1 = random.nextInt(10);
        int forn1 = random.nextInt(10);
        String last = one1 + "" + two1 + three1 + forn1;
        String phone = title + middle + last;
        return phone;
    }

    public String sendsslpostParam(DefaultHttpClient client, String req_url, String action,
                                   String param, String tag, String token, String aes_auth) {
        try {
            action = (String) invoke.invokeFunction("encrypt", action,
                    aes_auth.toString() + token + "VwxG&vJSrS-3*?7z");//
            action = action.substring(0, 40) + aes_auth + token + action.substring(40);
            param = (String) invoke.invokeFunction("encrypt", param,
                    aes_auth.toString() + token + "VwxG&vJSrS-3*?7z");// +token+"VwxG&vJSrS-3*?7z
            param = param.substring(0, 40) + aes_auth + token + param.substring(40);

            HttpPost httpPost = new HttpPost(req_url.split("\\?")[0]);
            httpPost.setHeader("X-Auth-Token", "" + token);
            httpPost.setHeader("Referer", "https://www.cebupacificair.com");
            httpPost.setHeader("Origin", "https://www.cebupacificair.com");
            httpPost.addHeader("Accept-Encoding", "gzip, deflate, br");
            httpPost.setHeader("content", action);
            httpPost.setHeader("scope", "omnix");
            if (tag.equals("登陆")) {
                httpPost.setHeader("uniqueid", "Qon2Gj2N6Mneim");
            }
            httpPost.setHeader("User-Agent", USER_AGENT);
            httpPost.setHeader("Authorization", "Bearer " + aes_auth);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("content", param);
            httpPost.setEntity(new StringEntity(jsonObject.toString(), "UTF-8"));
            HttpResponse execute = client.execute(httpPost);
            logger.info("--" + "{}{}本次请求的Response {}" + execute);
            String html = readHtmlContentFromEntity(execute.getEntity());
            if (execute.getStatusLine().getStatusCode() != 200) {
                log.info(html);
            }
            return html;
        } catch (Exception e) {
            logger.info(e.getMessage(), e);
            return null;
        }
    }

    public List<Map<String, List<Flight>>> mobileSeach(String fromCity, String toCity,
                                                       String fromDate, String retDate, String unique_id, DefaultHttpClient client) {

        // 手机价格 是没有 手续费得，但是有得航班必须加手续费 才是官网价格
        List<Map<String, List<Flight>>> rtn = new ArrayList<>();
        String ip = "";
        Map<String, String> re = null;
        String ip_rand = "";
        try {
            token = "";
            aes_auth = "";
            total_token = "";
//            if (stage.equals("search")) {
//                total_token = locklist.poll();
//            }
            try {
                if (StringUtils.isBlank(total_token) || StringUtils.isBlank(total_token)) {
                    String timestamp1 = rand();
                    Map<String, String> getauth = sendPostSO("v2/accessToken", "", "&" + timestamp1, XAT, "accessToken", unique_id, client);
                    String code = getauth.get("code");
                    for (int i = 0; i < 3; i++) {
                        if (!code.equals("200")) {
                            getauth = sendPostSO("v2/accessToken", "", "&" + timestamp1, XAT, "accessToken", unique_id, client);
                            code = getauth.get("code");
                        }

                    }
                    if (!code.equals("200")) {
                        throw new RuntimeException("获取token失败");
                    }
//                    logger.info("--" + "CEB获取Auth结果:" + getauth.get("code"));
                    String result = getauth.get("content");
//                    logger.info("--" + "CEB获取AuthResult:" + result);
                    aes_auth = JSONObject.parseObject(result).getString("Authorization");
                    token = JSONObject.parseObject(result).getString("X-Auth-Token") + "&"
                            + timestamp1;
                    total_token = token + "@@@" + aes_auth;
                    // log.info("==》" + token);
                    // log.info("==》" + aes_auth);
                    // if (StringUtils.isNotBlank(token)) {
                    // JRedisUtil.setKey("PHP5A_SEARCH_TOKE", token, 5);
                    // JRedisUtil.setKey("PHP5A_SEARCH_AUTH", aes_auth, 5);
                    // }
                } else {
                    token = total_token.split("@@@")[0];
                    aes_auth = total_token.split("@@@")[1];
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new RuntimeException("获取token失败");
            }

            if (StringUtils.isBlank(aes_auth) || StringUtils.isBlank(token)) {
                throw new RuntimeException("获取token失败");
            }


            // String cur = isCur(fromCity);
            // if (cur.equalsIgnoreCase("HKD")) {
            // hasAdministrativeFee = false;
            // }

            String nonce = System.currentTimeMillis() + "";
            String hashString = uniqueid + "/availability" + nonce + aes_auth;
            log.info("搜索UU" + uniqueid);
            String params = "{" +
                    "  \"lffMode\": false," +
                    "  \"rebook\": false," +
                    "  \"hash\": " + "\"" + generateHMACForSearch(hashString) + "\"," +
                    "  \"nonce\": " + "\"" + nonce + "\"," +
                    (!hasAdministrativeFee ? "  \"ssrs\": []," : "  \"ssrs\": [" + "    \"MAFI\"" + "  ],") +
                    "  \"routes\": [" +
                    "    {" +
                    "      \"origin\": \"" + fromCity + "\"," +
                    "      \"destination\": \"" + toCity + "\"," +
                    "      \"beginDate\": \"" + fromDate.substring(4, 6) + "/" + fromDate.substring(6, 8) + "/" + fromDate.substring(0, 4) + "\"" +
                    "    }" +
                    (   // 返程route
                            StringUtils.isNotBlank(retDate) ?
                                    ",{" +
                                            "\"origin\": \"" + toCity + "\"," +
                                            "\"destination\": \"" + fromCity + "\"," +
                                            "\"beginDate\": \"" + retDate.substring(4, 6) + "/" + retDate.substring(6, 8) + "/" + retDate.substring(0, 4) + "\"" +
                                            "}"
                                    :
                                    ""
                    ) +
                    "  ]," +
                    "  \"daysToLeft\": 0," +
                    "  \"daysToRight\": 6," +
                    "  \"version\": 2," +
                    "  \"adultCount\": " + (adult + child + infant) + "," +
                    "  \"childCount\": 0," +
                    "  \"infantCount\": {" +
                    "    \"lap\": 0," +
                    "    \"seat\": 0" +
                    "  }," +
                    "  \"promoCode\": \"\"," +
                    "  \"currency\": \"" + currency_code + "\"" +
                    "}";
            try {
                ip_rand = token.split("&")[1];
            } catch (Exception e) {

            }
            Map<String, String> search_result = sendPostSO("availability", JSONObject.parseObject(params).toString(), token, aes_auth, "搜索航班", unique_id, client);
            re = search_result;
            String code = search_result.get("code");
            String result = search_result.get("content");
            search = result;
            searchContent = result;
//            if (!code.equals("200")) {
//                writeToFile("失败航段:" + fromCity + "-" + toCity + ",失败时间:" + fromDate, "/Users/<USER>/Downloads/5J行李失败newwww.txt");
//                throw new DataException("搜索航班失败");
//            }

            // if("search".equals(stage)&&(result.contains("Invalid market"))){
            // JRedisUtil.setKey("NODATA2" + fromCity + toCity + fromDate, "1", 24 * 3600);
            // throw new DataException();
            // }

            // 如果包含451重新获取token、重新搜索航班
            if ((result.contains("errorcode\": \"451") || result.contains("Bad request"))
                    && !result.contains("Invalid market")) {
                String timestamp1 = rand();
                Map<String, String> getauth = sendPostSO("v2/accessToken", "", "&" + timestamp1,
                        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb21wYW55IjoiQ0VCVSBBaXIgSW5jLiIsIm5hbWUiOiJvbW5pWCIsInZlcnNpb24iOjEsImNvZGVWZXJzaW9uIjoiUHRFRHNWNVA1QUhMb0FoWnk3eHE1SW5XbGZOTW9WaDkifQ.rJdOfwQPOLGObQUkZOX0eEfpXmqHtAkeXNLjorQvQj4",
                        "accessToken", unique_id, client);
                String tokenCode = getauth.get("code");
                logger.info(unique_id + "--" + "重试CEB获取token状态码:" + tokenCode);
                String tokenResult = getauth.get("content");
                logger.info(unique_id + "--" + "重试CEB获取token结果:" + tokenResult);
                aes_auth = JSONObject.parseObject(tokenResult).getString("Authorization");
                token = JSONObject.parseObject(tokenResult).getString("X-Auth-Token") + "&"
                        + timestamp1;
                total_token = token + "@@@" + aes_auth;

                // 重新搜索航班
                search_result = sendPostSO("availability", JSONObject.parseObject(params).toString(),
                        token, aes_auth, "", unique_id, client);
                code = search_result.get("code");
                result = search_result.get("content");
            }
            logger.info(unique_id + result);
            if (result.indexOf("Multiple services match the specified type") > 0) {
                throw new RuntimeException();
            }

            // if("search".equals(stage)&&(result.contains("Invalid market"))){
            // JRedisUtil.setKey("NODATA2" + fromCity + toCity + fromDate, "1", 24 * 3600);
            // throw new DataException();
            // }

            if (code.equals("200")) {
                if (result.contains("currencyCode")) {
                    logger.info(unique_id + "--" + "宿务搜索成功-" + code);
//                    if (!stage.equals("book") && !"order".equals(stage)) {
//                        locklist.offer(total_token);
//                    }
                }
                JSONObject result_json = JSONObject.parseObject(result);
//                if (result.contains("451")) {
//                    throw new RuntimeException("查询航班列表为空:出发:" + fromCity + ",到达:" + toCity + ",日期:" + fromDate);
//                }
                // 去程解文
                rtn.add(parserRouting(result_json.getJSONArray("routes"),
                        result_json.getString("currencyCode"), false));
                // 返程解析
                if (StringUtils.isNotBlank(retDate))
                    rtn.add(parserRouting(result_json.getJSONArray("routes"),
                            result_json.getString("currencyCode"), true));
                if ("search".equals(stage) && rtn.size() == 0) {
                    Routing nullRouting = new Routing();
                    nullRouting.getExtraInfo().put("nodata", "");
                    // JRedisUtil.setKey("NODATA2" + fromCity + toCity + fromDate, "1", 3 * 24 *
                    // 3600);
                    logger.info(unique_id + "--" + fromCity + toCity + fromDate + "宿务航班数量为0");
                    throw new RuntimeException("航班数量为0");
                }
            } else {
                logger.info(unique_id + "--" + "宿务搜索状态码不对-" + re);
                throw new RuntimeException("查询航班列表失败:出发:" + fromCity + ",到达:" + toCity + ",日期:" + fromDate);
            }
        } catch (Exception exp) {
//            logger.info(unique_id +"--" + "宿务请求解析异常_" + ip_rand + "-" + re.toString().replaceAll("\n", ""));
            exp.printStackTrace();
//            log.info(" php5j ip " + ip);
            throw exp;
        }
        return rtn;
    }


    private String rand() {
        StringBuilder str = new StringBuilder();// 定义变长字符串
        Random random = new Random();
        // 随机生成数字，并添加到字符串
        for (int i = 0; i < 8; i++) {
            str.append(random.nextInt(10));
        }
        return str.toString();
    }

    public static String generateHMAC(String unid, String notice) {
        try {
            String message = unid + XAT + notice;  // Equivalent to `fe`
            String algorithm = "HmacSHA256";
            SecretKeySpec secretKeySpec = new SecretKeySpec(SECR.getBytes(StandardCharsets.UTF_8), algorithm);
            Mac mac = Mac.getInstance(algorithm);
            mac.init(secretKeySpec);
            byte[] hmacBytes = mac.doFinal(message.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hmacBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String generateHMACForSearch(String content) {
        try {
            String BSKY = "kAYDgiHzp0TeNkgUjMdHFANw3pATzAjE";
            String algorithm = "HmacSHA256";
            SecretKeySpec secretKeySpec = new SecretKeySpec(BSKY.getBytes(StandardCharsets.UTF_8), algorithm);
            Mac mac = Mac.getInstance(algorithm);
            mac.init(secretKeySpec);
            byte[] hmacBytes = mac.doFinal(content.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hmacBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String generateHMACNew(String unid, String notice, String aes_auth) {
        try {
            String key = "kAYDgiHzp0TeNkgUjMdHFANw3pATzAjE";
            String message = unid
                    + "/availability"
                    + notice
                    + aes_auth; // Equivalent to `fe`

            log.info("message----" + message);
            String algorithm = "HmacSHA256";
            SecretKeySpec secretKeySpec =
                    new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), algorithm);
            Mac mac = Mac.getInstance(algorithm);
            mac.init(secretKeySpec);
            byte[] hmacBytes = mac.doFinal(message.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hmacBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public Map<String, String> sendPostSO(String action, String params, String token,
                                          String aes_auth, String tag, String unique_id, DefaultHttpClient client) {
        JSONObject headers = new JSONObject();

        token = token.split("&")[0];

        JSONObject sendInfo = new JSONObject();
        //目标url
        sendInfo.put("url", "https://soar.cebupacificair.com/ceb-omnix-proxy-v2/" + action);
        HttpPost httpPost = new HttpPost("https://soar.cebupacificair.com/ceb-omnix-proxy-v2/" + action);


        String x_path = null;
        //目标网站请求头
        try {
            try {
                x_path = (String) J5Task.invoke.invokeFunction("encrypt", "/" + action, AESK);
            } catch (ScriptException e) {
                throw new RuntimeException(e);
            } catch (NoSuchMethodException e) {
                throw new RuntimeException(e);
            }

            if (action.equals("v2/accessToken")) {
                uniqueid = UUID.randomUUID().toString().toLowerCase();
                log.info("开始UU" + uniqueid);
//                action = (String) PHP5JTask.invoke.invokeFunction("encrypt", action, PARK);
                headers.put("uniqueid", uniqueid);
                httpPost.setHeader("uniqueid", uniqueid);
                String notice = System.currentTimeMillis() + "";
                JSONObject jsonObject_pa = new JSONObject();
                jsonObject_pa.put("message", generateHMAC(uniqueid, notice));
                jsonObject_pa.put("uniqueId", uniqueid);
                jsonObject_pa.put("nonce", notice);
                // log.info(jsonObject_pa.toString());
                params = (String) J5Task.invoke.invokeFunction("encrypt", jsonObject_pa.toString(), PARK);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("content", params);
                params = jsonObject.toString();
            } else {
//                action = (String) PHP5JTask.invoke.invokeFunction("encrypt", action, aes_auth + token + PARK);
//                action = action.substring(0, 40) + aes_auth + token + action.substring(40);
//                log.info("加密前参数::"+params);
                params = (String) J5Task.invoke.invokeFunction("encrypt", params, aes_auth + token + PARK);
                params = params.substring(0, 40) + aes_auth + token + params.substring(40);
//                log.info("加密后参数::"+params);

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("content", params);
                params = jsonObject.toString();
            }

            if (StringUtils.isNotBlank(token)) {
                headers.put("X-Auth-Token", token);
                httpPost.setHeader("X-Auth-Token", token);
            }
            if (StringUtils.isNotBlank(aes_auth)) {
                headers.put("Authorization", "Bearer " + aes_auth);
                httpPost.setHeader("Authorization", "Bearer " + aes_auth);
            }
            headers.put("referer", "https://www.cebupacificair.com");
            httpPost.setHeader("referer", "https://www.cebupacificair.com");
            headers.put("origin", "https://www.cebupacificair.com");
            httpPost.setHeader("origin", "https://www.cebupacificair.com");
            // headers.put("content", action);
            headers.put("x-path", x_path);
            httpPost.setHeader("x-path", x_path);
            headers.put("content-type", "application/json");
            httpPost.setHeader("content-type", "application/json");
//            headers.put("accept", "application/json, text/plain, */*");
            headers.put("accept-encoding", "gzip;q=1.0, compress;q=0.5");
            httpPost.setHeader("content-type", "application/json");
            headers.put("accept-language", getaccept_lunage());
            httpPost.setHeader("accept-language", getaccept_lunage());
            headers.put("User-Agent", USER_AGENT);
            httpPost.setHeader("User-Agent", USER_AGENT);
            headers.put("accept", "*/*");
            httpPost.setHeader("accept", "*/*");
            headers.put("host", "soar.cebupacificair.com");
            httpPost.setHeader("host", "soar.cebupacificair.com");
        } catch (Exception e) {
            e.printStackTrace();
        }

//        String time_out = config.getProperty("time.out");
        sendInfo.put("headers", headers);
        sendInfo.put("method", "post");
        sendInfo.put("tls_type", "curl_cffi");
        sendInfo.put("proxy_str", proxy_str);
        sendInfo.put("timeout", "30");
        if (!params.equals("")) {
            com.alibaba.fastjson.JSONObject jsonObject2 = new com.alibaba.fastjson.JSONObject();
            String content1 = JSONObject.parseObject(params).getString("content");
            jsonObject2.put("content", content1);
            sendInfo.put("data", jsonObject2);
        }

        String content = "";
        String code = "";
        try {


            X509TrustManager xtm = new X509TrustManager() { // 创建TrustManager
                public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                }

                public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                }

                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
            };
            // TLS1.0与SSL3.0基本上没有太大的差别，可粗略理解为TLS是SSL的继承者，但它们使用的是相同的SSLContext
            SSLContext ctx = null;
            try {
                ctx = SSLContext.getInstance("TLS");
                // 使用TrustManager来初始化该上下文，TrustManager只是被SSL的Socket所使用
                ctx.init(null, new TrustManager[]{xtm}, null);
                // 创建SSLSocketFactory
                SSLSocketFactory socketFactory = new SSLSocketFactory(ctx);
                // 通过SchemeRegistry将SSLSocketFactory注册到我们的HttpClient上
                client.getConnectionManager().getSchemeRegistry().register(new Scheme("https", 443, socketFactory));
                // 设置超时时间
                client.getParams().setParameter(CoreConnectionPNames.SO_TIMEOUT, 30 * 1000);
                client.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, 6000 * 5);

            } catch (NoSuchAlgorithmException e) {

                throw new RuntimeException(e);
            } catch (KeyManagementException e) {

                throw new RuntimeException(e);
            }

//            HttpClient client = daili();
//
//
//             HttpGet httpGet = new HttpGet("https://httpbin.org/ip");
//
//            httpGet.setHeader("Host", "httpbin.org");
//            httpGet.setHeader("upgrade-insecure-requests", "1");
//            httpGet.setHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
//            httpGet.setHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
//            httpGet.setHeader("accept-language", getaccept_lunage());
//            httpGet.setHeader("priority", "u=0, i");
//             HttpResponse execute1 = client.execute(httpGet);
//             content = HttpUtil.readHtmlContentFromEntity(execute1.getEntity());
            if (action.equals("availability")) {
                HttpPost httpPost1 = new HttpPost("https://soar.cebupacificair.com/ceb-omnix-proxy-v2/" + action);
                httpPost1.setHeader("Host", "soar.cebupacificair.com");
                httpPost1.setHeader("pragma", "no-cache");
                httpPost1.setHeader("cache-control", "no-cache");
                httpPost1.setHeader("sec-ch-ua-platform", "\"macOS\"");
                httpPost1.setHeader("x-auth-token", token);
                httpPost1.setHeader("authorization", "Bearer "+aes_auth);
                httpPost1.setHeader("x-path", x_path);
                httpPost1.setHeader("sec-ch-ua", "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"");
                httpPost1.setHeader("sec-ch-ua-mobile", "?0");
                httpPost1.setHeader("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
                httpPost1.setHeader("accept", "application/json, text/plain, */*");
                httpPost1.setHeader("content-type", "application/json");
                httpPost1.setHeader("origin", "https://www.cebupacificair.com");
                httpPost1.setHeader("sec-fetch-site", "same-site");
                httpPost1.setHeader("sec-fetch-mode", "cors");
                httpPost1.setHeader("sec-fetch-dest", "empty");
                httpPost1.setHeader("referer", "https://www.cebupacificair.com/");
                httpPost1.setHeader("accept-language", "zh-CN,zh;q=0.9");
                httpPost1.setHeader("priority", "u=1, i");
                httpPost1.setEntity(new StringEntity(params, "UTF-8"));
                HttpResponse execute = client.execute(httpPost1);
                logger.info("{}>>{}请求Response>>{}" + "1" + tag + execute.getStatusLine().toString());
                code = execute.getStatusLine().getStatusCode() + "";
                content = readHtmlContentFromEntity(execute.getEntity());
                if (execute.getStatusLine().getStatusCode() >= 400) {
                    logger.info("{}>>{}请求异常返回>>{}" + "order_no" + tag + content);
                }
            }
            httpPost.setEntity(new StringEntity(params, "UTF-8"));
            HttpResponse execute = client.execute(httpPost);
            logger.info("{}>>{}请求Response>>{}" + "1" + tag + execute.getStatusLine().toString());
            code = execute.getStatusLine().getStatusCode() + "";
            content = readHtmlContentFromEntity(execute.getEntity());
            if (execute.getStatusLine().getStatusCode() >= 400) {
                logger.info("{}>>{}请求异常返回>>{}" + "order_no" + tag + content);
            }

        } catch (Exception e) {
            logger.info("{}>>{}请求异常返回>>{}" + "order_no" + tag + content);
            e.printStackTrace();
        }


        HashMap<String, String> dataMap = new HashMap<>();
        dataMap.put("code", code);
        dataMap.put("content", content);
//        log.info(tag + "请求状态码:" + code);
//        log.info(tag + "请求content:" + content);
        // try {
        // JSONObject jsonObject1 = new JSONObject(content);
        // // if(StringUtils.isNotwBlank(jsonObject1.getString("response"))){
        // content = jsonObject1.getString("response");
        // map.put("content", response);
        // // }
        // } catch (Exception e) {
        // logger.info("解析curl_tls_返回报文异常" + e.getMessage() + map);
        // return map;
        // }

        return dataMap;
    }

    public static HttpClient daili() {
        // 代理服务器地址和端口
        String proxyHost = "*************";
        int proxyPort = 22323;

        // 代理服务器的用户名和密码
        String proxyUsername = "14aefbf0fbb3f";
        String proxyPassword = "dd83b0be8e";

        // 创建 HttpHost 对象
        HttpHost proxy = new HttpHost(proxyHost, proxyPort, "http");

        // 创建 CredentialsProvider 对象
        CredentialsProvider credsProvider = new BasicCredentialsProvider();
        credsProvider.setCredentials(
                new AuthScope(proxyHost, proxyPort),
                new UsernamePasswordCredentials(proxyUsername, proxyPassword)
        );

        // 创建 HttpClient 并设置代理
        HttpClient client = HttpClients.custom()
                .setDefaultCredentialsProvider(credsProvider)
                .setProxy(proxy)
                .build();
        return client;
    }


    public static void writeToFile(String content, String filePath) {
        // 如果未配置日志路径,直接跳过
//        String configPath = JRedisUtil.getDebugLogPath();
//        if (configPath == null || configPath.trim().isEmpty()) {
//            return;
//        }

        try {
            File file = new File(filePath);
            // 确保目录存在
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            try (BufferedWriter writer = new BufferedWriter(new FileWriter(file, true))) {
                writer.write(content);
                writer.newLine(); // 如果需要换行
                logger.info("调试信息已写入文件: {}", filePath);
            }
        } catch (IOException e) {
            logger.info("写入调试日志失败: {}", e.getMessage());
        }
    }


    static Random random = new Random();

    public String getaccept_lunage() {
        String init_str = "zh-CN,zh;q=0.9";
        int i = random.nextInt(luanges.size());
        for (int j = 0; j < i; j++) {
            init_str = init_str + "," + luanges.get(random.nextInt(luanges.size()));
        }
        // log.info(init_str);
        return init_str;
    }


    private Map<String, List<Flight>> parserRouting(JSONArray routing, String currencyCode,
                                                    boolean isRt) {
        Map<String, List<Flight>> map = new HashMap<>();
        try {

            JSONArray jsonArray;
            if (isRt) {
                jsonArray = routing.getJSONObject(1).getJSONArray("journeys");
            } else {
                jsonArray = routing.getJSONObject(0).getJSONArray("journeys");
            }

            if (jsonArray == null || jsonArray.size() < 1) {
                return map;
            }

            for (int i = 0; i < jsonArray.size(); i++) {

                JSONObject routerJson = jsonArray.getJSONObject(i);
                JSONArray segmentsJson = routerJson.getJSONArray("segments");
                List<Flight> list = new ArrayList<>();
                String fareClass = routerJson.getString("fareClass");
                for (int j = 0; j < segmentsJson.size(); j++) {
                    JSONObject flightJson = segmentsJson.getJSONObject(j);
                    Flight f = new Flight();

                    String carrierCode =
                            flightJson.getJSONObject("identifier").getString("carrierCode");
                    String equipment = flightJson.getString("equipmentType");
                    String departureDate =
                            flightJson.getJSONObject("designator").getString("departure");
                    //2025-07-29T11:00:00
                    departureDate = departureDate.replaceAll("\\D", "");
                    departureDate = departureDate.substring(0, 12);
                    String arrivalDate = flightJson.getJSONObject("designator").getString("arrival");

                    arrivalDate = arrivalDate.replaceAll("\\D", "");
                    arrivalDate = arrivalDate.substring(0, 12);

                    String routeNumber = flightJson.getJSONObject("identifier").getString("identifier");
                    routeNumber = routeNumber.replaceAll("\\ +", "");
                    String departureStation =
                            flightJson.getJSONObject("designator").getString("origin");
                    String arrivalStation =
                            flightJson.getJSONObject("designator").getString("destination");

                    f.setCarrier(carrierCode);
                    f.setFlightNumber(carrierCode + routeNumber);
                    f.setAircraftCode(equipment);
                    f.setDepTime(departureDate);
                    f.setArrTime(arrivalDate);
                    f.setDepAirport(departureStation);
                    f.setArrAirport(arrivalStation);
                    f.setCabin(fareClass);
                    f.setSegmentType(1);
                    list.add(f);
                }

                JSONArray bundles = routerJson.getJSONArray("bundles");

                String combo = "";
                for (int j = 0; j < bundles.size(); j++) {
                    JSONObject bundle = bundles.getJSONObject(j);
                    String bundleCode = bundle.getString("bundleCode");
                    String amount = bundle.getString("amount");
                    combo = bundleCode + "=" + amount;
                }

                String price = routerJson.getString("fareTotal");

                if (StringUtils.isBlank(price)) {
                    continue;
                }

                int available = routerJson.getIntValue("availableCount");
                String flight_key = routerJson.getString("journeyKey");
                String fare_key = routerJson.getString("fareAvailabilityKey");
                JSONArray segments = routerJson.getJSONArray("segments");
                String segmentKey = "";
                for (int j = 0; j < segments.size(); j++) {
                    JSONObject segment = segments.getJSONObject(j);
                    segmentKey = segmentKey + "#" + segment.getString("segmentKey");
                }
                // int pp = (int) Double.parseDouble(exchange(price, currencyCode)) - 10;
                double pp = Double.parseDouble(price);

                map.put(i + "_" + pp + "_" + 10 + "_" + available + "_&" + flight_key + "&" + fare_key
                        + "&" + currencyCode + "&" + price + "&" + segmentKey + "&" + combo, list);
            }

            return map;

        } catch (Exception e) {
            logger.info("解析routing异常" + routing);
            logger.info("解析routing异常" + e.getMessage());

            return null;
        }

    }


    public void setCurrence(String currence) {
        this.currence = currence;
    }

    public String sendGetRequest(HttpClient httpClient, String url, String token) {
        String responseContent = null; // 响应内容
        HttpGet getMethod = new HttpGet(url);
        getMethod.setHeader("Host", "mobile-api.cebupacificair.com");
        getMethod.setHeader("Authorization-Secret", entryToken(token, false, url));
        getMethod.setHeader("User-Agent", USER_AGENT);
        getMethod.setHeader("Connection", "keep-alive");
        getMethod.setHeader("Accept", "application/json");
        getMethod.setHeader("Accept-Language", "zh-cn");
        if (!"".equals(token) && token != null) {
            getMethod.setHeader("Authorization", token);
        }
        getMethod.setHeader("Accept-Encoding", "gzip,deflate");
        getMethod.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);
        try {
            HttpResponse response = httpClient.execute(getMethod);
            HttpEntity entity = response.getEntity();

            entity = response.getEntity();
            responseContent = readHtmlContentFromEntity(entity);
            getMethod.releaseConnection();
            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public String sendSSLPatchRequestByJson(HttpClient httpClient, String reqURL, String params,
                                            String token) {
        String responseContent = null; // 响应内容

        try {
            HttpPatch httpPatch = new HttpPatch(reqURL); // 创建HttpPost

            httpPatch.setEntity(new StringEntity(params));
            httpPatch.setHeader("Host", "mobile-api.cebupacificair.com");
            // httpPatch.setHeader("loggly-tracking-id", LOGGLY_TRACKING_ID);
            httpPatch.setHeader("Accept", "application/json");
            if (!"".equals(token) && token != null) {
                httpPatch.setHeader("Authorization", token);
            }
            httpPatch.setHeader("Accept-Language", "zh-cn");
            httpPatch.setHeader("Accept-Encoding", "gzip,deflate");
            httpPatch.setHeader("Content-Type", "application/json; charset=utf-8");
            httpPatch.setHeader("User-Agent", USER_AGENT);
            httpPatch.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);
            httpPatch.setHeader("Connection", "keep-alive");
            httpPatch.setHeader("Authorization-Secret", entryToken(token, false, reqURL));

            // token
            HttpResponse response = httpClient.execute(httpPatch); // 执行POST请求

            log.info("httpclicent" + response.getStatusLine().getStatusCode());

            HttpEntity entity = response.getEntity(); // 获取响应实体
            responseContent = readHtmlContentFromEntity(entity);
            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return responseContent;
        }
    }


    public String sendSSLPatchRequestByJson(HttpClient httpClient, String reqURL, JSONObject params,
                                            String token) {
        long responseLength = 0;
        String responseContent = null; // 响应内容

        try {
            HttpPatch httpPatch = new HttpPatch(reqURL); // 创建HttpPost

            httpPatch.setEntity(new StringEntity(params.toString()));
            httpPatch.setHeader("Host", "mobile-api.cebupacificair.com");
            // httpPatch.setHeader("loggly-tracking-id", LOGGLY_TRACKING_ID);
            httpPatch.setHeader("Accept", "application/json");
            if (!"".equals(token) && token != null) {
                httpPatch.setHeader("Authorization", token);
            }
            httpPatch.setHeader("Accept-Language", "zh-cn");
            httpPatch.setHeader("Accept-Encoding", "gzip,deflate");
            httpPatch.setHeader("Content-Type", "application/json; charset=utf-8");
            // httpPatch.setHeader("User-Agent", USER_AGENT);
            httpPatch.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);
            httpPatch.setHeader("Connection", "keep-alive");
            httpPatch.setHeader("Authorization-Secret", entryToken(token, false, reqURL));

            // token
            HttpResponse response = httpClient.execute(httpPatch); // 执行POST请求

            log.info("httpclicent" + response.getStatusLine().getStatusCode());

            HttpEntity entity = response.getEntity(); // 获取响应实体
            responseContent = readHtmlContentFromEntity(entity);
            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return responseContent;
        }
    }


    public String getToken(HttpClient client, String userName) {
        try {
            String url = "https://mobile-api.cebupacificair.com/dotrez-prod-v3/api/nsk/v1/Token";
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Host", "mobile-api.cebupacificair.com");
            httpPost.setHeader("Authorization-Secret", entryToken("", false,
                    "https://mobile-api.cebupacificair.com/dotrez-prod-v3/api/nsk/v1/Token"));
            httpPost.setHeader("Content-Type", "application/json; charset=utf-8");
            httpPost.setHeader("Connection", "keep-alive");
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("Accept-Language", "zh-cn");
            httpPost.setHeader("User-Agent", USER_AGENT);
            httpPost.setHeader("Accept-Encoding", "gzip,deflate");
            httpPost.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);

            httpPost.setEntity(new StringEntity("{\"Credentials\":{" + "\"Username\":\"" + userName
                    + "\"," // 邮箱
                    + "\"Password\":\"Axqt599999\"," // 密码
                    + "\"Domain\":\"WWW\",\"Location\":\"MOB\"},\"ApplicationName\":\"cebumobileapp\"}"));
            HttpResponse response = client.execute(httpPost);
            HttpEntity entity = response.getEntity();
            String result = readHtmlContentFromEntity(entity);

            String token = JSONObject.parseObject(result).getJSONObject("data").getString("token");
            log.info(token);
            httpPost.releaseConnection();
            return token;
        } catch (Exception e) {
            try {
                String url =
                        "https://mobile-api.cebupacificair.com/dotrez-prod-v3/api/nsk/v1/Token";
                HttpPost httpPost = new HttpPost(url);
                httpPost.setHeader("Host", "mobile-api.cebupacificair.com");
                httpPost.setHeader("Authorization-Secret", AUTHORIZATION_SECRET);
                httpPost.setHeader("Content-Type", "application/json; charset=utf-8");
                httpPost.setHeader("Connection", "keep-alive");
                httpPost.setHeader("Accept", "application/json");
                httpPost.setHeader("Accept-Language", "zh-cn");
                httpPost.setHeader("User-Agent", USER_AGENT);
                httpPost.setHeader("Accept-Encoding", "gzip,deflate");
                httpPost.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);

                httpPost.setEntity(new StringEntity("{\"Credentials\":{" + "\"Username\":\""
                        + userName + "\"," // 邮箱
                        + "\"Password\":\"Axqt599999\"," // 密码
                        + "\"Domain\":\"WWW\",\"Location\":\"MOB\"},\"ApplicationName\":\"cebumobileapp\"}"));
                HttpResponse response = client.execute(httpPost);
                HttpEntity entity = response.getEntity();
                String result = readHtmlContentFromEntity(entity);
                String token =
                        JSONObject.parseObject(result).getJSONObject("data").getString("token");

                httpPost.releaseConnection();
                return token;
            } catch (Exception exp) {
                exp.printStackTrace();
            }
        }
        return null;
    }


    /**
     * 向HTTPS地址发送POST请求
     *
     * @param reqURL 请求地址
     * @param params 请求参数
     * @return 响应内容
     */
    @SuppressWarnings({"finally", "deprecation", "resource"})
    public String sendSSLPostRequestByJson(HttpClient httpClient, String reqURL, JSONObject params,
                                           String token) {
        long responseLength = 0;
        String responseContent = null; // 响应内容

        try {
            HttpPost httpPost = new HttpPost(reqURL); // 创建HttpPost

            log.info(params.toString().replaceAll("\"null\"", "null"));
            httpPost.setEntity(new StringEntity(params.toString().replaceAll("\"null\"", "null")));
            httpPost.setHeader("Host", "mobile-api.cebupacificair.com");
            httpPost.setHeader("Accept", "application/json");
            if (!"".equals(token) && token != null) {
                httpPost.setHeader("Authorization", token);
            }
            // httpPost.addHeader("loggly-tracking-id", "340631");
            httpPost.setHeader("Accept-Encoding", "gzip,deflate");
            httpPost.setHeader("Content-Type", "application/json; charset=utf-8");
            httpPost.setHeader("Connection", "keep-alive");
            httpPost.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);
            httpPost.setHeader("Connection", "keep-alive");
            httpPost.setHeader("Authorization-Secret", AUTHORIZATION_SECRET);
            HttpResponse response = httpClient.execute(httpPost); // 执行POST请求

            log.info("httpclicent" + response.getStatusLine().getStatusCode());

            HttpEntity entity = response.getEntity(); // 获取响应实体
            responseContent = readHtmlContentFromEntity(entity);

            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return responseContent;
        }
    }

    /**
     * 向HTTPS地址发送POST请求
     *
     * @param reqURL 请求地址
     * @param params 请求参数
     * @return 响应内容
     */
    @SuppressWarnings({"finally", "deprecation", "resource"})
    public String sendSSLPostRequestByJson(HttpClient httpClient, String reqURL, String params,
                                           String token) {
        long responseLength = 0;
        String responseContent = null; // 响应内容

        try {
            log.info("--" + reqURL + "--");

            HttpPost httpPost = new HttpPost(reqURL); // 创建HttpPost

            httpPost.setEntity(new StringEntity(params));
            httpPost.setHeader("Host", "mobile-api.cebupacificair.com");
            httpPost.setHeader("Accept", "application/json");
            if (!"".equals(token) && token != null) {
                httpPost.setHeader("Authorization", token);
            }
            // httpPost.addHeader("loggly-tracking-id", "340631");
            httpPost.setHeader("Accept-Language", "zh-cn");
            httpPost.setHeader("Accept-Encoding", "gzip,deflate");
            httpPost.setHeader("Content-Type", "application/json; charset=utf-8");
            httpPost.setHeader("User-Agent", USER_AGENT);
            httpPost.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);
            httpPost.setHeader("Connection", "keep-alive");

            httpPost.setHeader("Authorization-Secret", entryToken(token, false, reqURL));

            // token
            HttpResponse response = httpClient.execute(httpPost); // 执行POST请求

            log.info("httpclicent" + response.getStatusLine().getStatusCode());

            HttpEntity entity = response.getEntity(); // 获取响应实体
            responseContent = readHtmlContentFromEntity(entity);
            if (null != entity) {
                responseLength = entity.getContentLength();
                EntityUtils.consume(entity); // Consume response content
            }

            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return responseContent;
        }
    }

    /**
     * 向HTTPS地址发送POST请求
     *
     * @param reqURL 请求地址
     * @return 响应内容
     */
    @SuppressWarnings({"finally", "deprecation", "resource"})
    public String sendSSLPostRequestByJsonPay(HttpClient httpClient, String reqURL, String token) {
        long responseLength = 0;
        String responseContent = null; // 响应内容

        try {
            log.info("--" + reqURL + "--");
            HttpPost httpPost = new HttpPost(reqURL); // 创建HttpPost

            httpPost.setEntity(new StringEntity(
                    "{\"Hold\":null,\"LatestReceivedBy\":\"iOS-71624.2.26.1\",\"LatestReceivedReference\":\"\",\"RestrictionOverride\":false,\"WaiveNameChangeFee\":false,\"WaivePenaltyFee\":false,\"WaiveSpoilageFee\":false,\"DistributeToContacts\":false}"));
            httpPost.setHeader("Host", "mobile-api.cebupacificair.com");
            httpPost.setHeader("Accept", "application/json");
            httpPost.addHeader("loggly-tracking-id", "340631");
            httpPost.setHeader("Authorization", token);
            httpPost.setHeader("Accept-Language", "zh-cn");
            httpPost.setHeader("Accept-Encoding", "gzip,deflate");
            httpPost.setHeader("Content-Type", "application/json; charset=utf-8");
            httpPost.setHeader("User-Agent", USER_AGENT);
            httpPost.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);
            httpPost.setHeader("Connection", "keep-alive");
            httpPost.setHeader("Authorization-Secret", AUTHORIZATION_SECRET);

            // token
            HttpResponse response = httpClient.execute(httpPost); // 执行POST请求

            log.info("httpclicent" + response.getStatusLine().getStatusCode());

            HttpEntity entity = response.getEntity(); // 获取响应实体
            responseContent = readHtmlContentFromEntity(entity);
            if (null != entity) {
                responseLength = entity.getContentLength();
                EntityUtils.consume(entity); // Consume response content
            }

            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return responseContent;
        }
    }

    /**
     * 向HTTPS地址发送POST请求
     *
     * @param reqURL 请求地址
     * @param params 请求参数
     * @return 响应内容
     */
    @SuppressWarnings({"finally", "deprecation", "resource"})
    public String sendSSLPUTRequestByJson(HttpClient httpClient, String reqURL, String params,
                                          String token) {
        long responseLength = 0;
        String responseContent = null; // 响应内容

        try {
            log.info("--" + reqURL + "--");
            HttpPut httpPut = new HttpPut(reqURL); // 创建HttpPost

            httpPut.setEntity(new StringEntity(params.toString()));
            httpPut.setHeader("Host", "mobile-api.cebupacificair.com");
            httpPut.setHeader("Accept", "application/json");
            if (!"".equals(token) && token != null) {
                httpPut.setHeader("Authorization", token);
            }
            httpPut.setHeader("Accept-Language", "zh-cn");
            httpPut.setHeader("Accept-Encoding", "gzip,deflate");
            httpPut.setHeader("Content-Type", "application/json; charset=utf-8");
            httpPut.setHeader("User-Agent", USER_AGENT);
            httpPut.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);
            httpPut.setHeader("Connection", "keep-alive");

            httpPut.setHeader("Authorization-Secret", AUTHORIZATION_SECRET);

            // token
            HttpResponse response = httpClient.execute(httpPut); // 执行POST请求

            log.info("httpclicent" + response.getStatusLine().getStatusCode());

            HttpEntity entity = response.getEntity(); // 获取响应实体
            responseContent = readHtmlContentFromEntity(entity);
            if (null != entity) {
                responseLength = entity.getContentLength();
                EntityUtils.consume(entity); // Consume response content
            }

            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return responseContent;
        }
    }


    private static final byte[] secret = "bmF2aXRhaXJlcHJvZmVzc2lvbmFsc2VydmljZXM=".getBytes();

    // 生成一个token
    public static String creatToken(Map<String, Object> payloadMap) {


        // 3.先建立一个头部Header
        /**
         * JWSHeader参数：1.加密算法法则,2.类型，3.。。。。。。。 一般只需要传入加密算法法则就可以。 这里则采用HS256
         *
         * JWSAlgorithm类里面有所有的加密算法法则，直接调用。
         */
        // JWSHeader jwsHeader = new JWSHeader(JWSAlgorithm.HS256);
        // //建立一个载荷Payload
        // net.minidev.json.JSONObject jsonObject = new net.minidev.json.JSONObject();
        // jsonObject.putAll(payloadMap);
        // Payload payload = new Payload(jsonObject);
        //
        // //将头部和载荷结合在一起
        // JWSObject jwsObject = new JWSObject(jwsHeader, payload);
        // //建立一个密匙
        // JWSSigner jwsSigner = new MACSigner(secret);
        // //签名
        // jwsObject.sign(jwsSigner);
        // 生成token
        return null;// jwsObject.serialize();
    }

    public static String entryToken(String token, boolean isFirst, String url) {
        Map<String, Object> payload = new HashMap<String, Object>();
        int max = 100000, min = 999999;
        int ran2 = (int) (Math.random() * (max - min) + min);
        String log = String.valueOf(ran2);
        payload.put("appname", "cebupacific");
        if (!isFirst) {
            payload.put("NSToken", token);
        }
        // } else {
        // return AUTHORIZATION_SECRET;
        // }
        // iOS-164977.2.68.0
        payload.put("version", "2.70.0");
        payload.put("platform", "IOS");
        payload.put("url", url);
        try {
            String retInner = creatToken(payload);
            return retInner;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";

    }


    private String isCur(String fromCity) {
        if (StringUtils.isNotBlank(currence)) {
            return currence;
        }

        String cur = "PHP";
        if ("SYD".equals(fromCity) || "MEL".equals(fromCity)) {
            cur = "AUD";
        } else if ("BWN".equals(fromCity)) {
            cur = "BND";
        } else if ("HKG".equals(fromCity)) {
            cur = "HKD";
        } else if ("MFM".equals(fromCity)) {
            cur = "MOP";
        } else if ("SIN".equals(fromCity)) {
            cur = "SGD";
        } else if ("BKK".equals(fromCity) || "DMK".equals(fromCity) || "CNX".equals(fromCity)) {
            cur = "THB";
        } else if ("TPE".equals(fromCity) || "KHH".equals(fromCity)) {
            cur = "TWD";
        } else if ("DXB".equals(fromCity)) {
            cur = "AED";
        } else if ("REP".equals(fromCity) || "DPS".equals(fromCity) || "CGK".equals(fromCity)
                || "GUM".equals(fromCity) || "HAN".equals(fromCity) || "SGN".equals(fromCity)
                || "DAD".equals(fromCity)) {
            cur = "USD";
        } else if ("PEK".equals(fromCity) || "PVG".equals(fromCity) || "XMN".equals(fromCity)
                || "CAN".equals(fromCity) || "SZX".equals(fromCity)) {
            cur = "CNY";
        } else if ("PEK".equals(fromCity) || "PVG".equals(fromCity) || "XMN".equals(fromCity)
                || "SZX".equals(fromCity) || "CAN".equals(fromCity)) {
            cur = "CNY";
        } else if ("FUK".equals(fromCity) || "NGO".equals(fromCity) || "NRT".equals(fromCity)
                || "KIX".equals(fromCity) || "CTS".equals(fromCity)) {
            cur = "JPY";
        } else if ("BKI".equals(fromCity) || "KUL".equals(fromCity) || "SDK".equals(fromCity)) {
            cur = "MYR";
        } else if ("ICN".equals(fromCity) || "PUS".equals(fromCity)) {
            cur = "KRW";
        }

        return cur;
    }


    public static void main(String[] a) throws Exception {
//        String s = generateHMACNew("cc1576d9-8163-4354-92f5-5c299b961bf1", "*************");
//        log.info(s);

        // for (int i = 0; i < 2; i++) {
        // Thread thread = new Thread(new Runnable() {
        // public void run() {
        // while (true){
        //// for (int j = 0; j < 1000; j++) {
        // com.ota.task.PHP5JTask task = new com.ota.task.PHP5JTask();
        // List<Routing> routings = new ArrayList<Routing>();
        // task.stage = "search";
        // task.adult = 1;
        // task.flightData = "5J5062";
        //
        // String param = "{\n" +
        // " \"unique_id\": \"249926fa-f561-452c-b388-55b3573c92d3\",\n" +
        // " \"site_code\": \"5J\",\n" +
        // " \"site_type\": \"airline\",\n" +
        // " \"dep_airport_code\": \"CEB\",\n" +
        // " \"arr_airport_code\": \"MNL\",\n" +
        // " \"dep_date\": \"2025-04-21\",\n" +
        // " \"return_date\": \"\",\n" +
        // " \"trip_type\": \"ow\",\n" +
        // " \"status\": \"pending\",\n" +
        // " \"airline_code\": \"5J\",\n" +
        // " \"schedule_id\": 744,\n" +
        // " \"fetch_rule_id\": 4,\n" +
        // " \"expire_seconds\": 1200,\n" +
        // " \"task_key\": \"5J-CEB-MNL-2025-04-21\",\n" +
        // " \"currency_code\": \"USD\",\n" +
        // " \"create_time\": \"2025-04-07T12:00:59\",\n" +
        // " \"start_time\": \"2025-04-07T12:02:09\",\n" +
        // " \"end_time\": \"2025-04-07T12:02:09\"\n" +
        // "}";
        //
        // task.wrapperRequest(param);
        // }
        // }
        // });
        // thread.start();
        // }

        // while (true){
        // for (int i = 0; i < 1; i++) {
        log.info(
                "========================================================================");
//        while (true) {
        try {
//            String[] curList = new String[]{"PHP", "USD", "JPY", "KRW", "CNY", "THB", "SGD", "AUD", "MOP", "HKD", "MYR", "TWD"};
            String[] curList = new String[]{"PHP"};
//
            for (String string : curList) {
                J5Task task = new J5Task();
                //
                List<Routing> routings = new ArrayList<Routing>();
                // task.site = "api.oneflya.com";
                task.stage = "search";
                task.adult = 1;
                task.flightData = "5J2504";
                String param = "{\n" + "    \"unique_id\": \"249926fa-f561-452c-b388-55b3573c92d3\",\n"
                        + "    \"site_code\": \"5J\",\n" + "    \"site_type\": \"airline\",\n"
                        + "    \"depCity\": \"HKG\",\n"
                        + "    \"arrCity\": \"CEB\",\n" + "    \"departureDate\": \"2025-08-07\",\n"
                        + "    \"return_date\": \"\",   \n" + "    \"trip_type\": \"ow\",\n"
                        + "    \"status\": \"pending\",\n" + "    \"airline_code\": \"5J\",\n"
                        + "    \"schedule_id\": 744,\n" + "    \"fetch_rule_id\": 4,\n"
                        + "    \"expire_seconds\": 1200,\n"
                        + "    \"task_key\": \"5J-CEB-MNL-2025-04-21\",\n"
                        + "    \"currency\": \"" + string + "\",\n"
                        + "    \"create_time\": \"2025-04-07T12:00:59\",\n"
                        + "    \"start_time\": \"2025-04-07T12:02:09\",\n"
                        + "    \"end_time\": \"2025-04-07T12:02:09\"\n" + "}";
                String search = task.wrapperRequest(param, "search");
                log.info(search);
            }
//                log.info("Routing：" + routings.size());
//                for (Routing routing : routings) {
//                    log.info(routing);
//                }
        } catch (Exception e) {
            e.printStackTrace();
        }

//        }
        // }

    }

    // 添加货币代码验证
    private boolean isValidCurrencyCode(String code) {
        if (StringUtils.isBlank(code)) {
            return false;
        }
        // 添加货币代码验证逻辑
        return code.matches("[A-Z]{3}");
    }

    // 添加货币代码处理
    private String processCurrencyCode(String code) {
        if (StringUtils.isBlank(code)) {
            return "USD";
        }
        return code.toUpperCase();
    }

    private String checkEDBagPrice(String selectCount, String owHtml, String flight_no, String fromCity, String toCity, String fromDate, String max, String cabin) {
        JSONObject searchJson = JSONObject.parseObject(owHtml);
        JSONObject selectJson = JSONObject.parseObject(selectCount);

        double searchTotal = 0.0;
        double fareTotal = 0.0;
        double amount = 0.0;
        JSONArray routes = searchJson.getJSONArray("routes");
        for (int i = 0; i < routes.size(); i++) {
            JSONObject route = routes.getJSONObject(i);
            JSONArray journeys = route.getJSONArray("journeys");
            for (int j = 0; j < journeys.size(); j++) {
                String flightNumber = "";
                JSONObject journey = journeys.getJSONObject(j);
//                String group = journey.getString("group");
//                if (!group.equals("PROMO")){
//                    continue;
//                }
                JSONArray segments = journey.getJSONArray("segments");
                for (int k = 0; k < segments.size(); k++) {
                    JSONObject segment = segments.getJSONObject(k);
                    String identifier = segment.getJSONObject("identifier").getString("identifier");
                    String carrierCode = segment.getJSONObject("identifier").getString("carrierCode");
                    if (flightNumber.equals("")) {
                        flightNumber = carrierCode + identifier;
                    } else {
                        flightNumber = flightNumber + "/" + carrierCode + identifier;
                    }
                }

                if (flight_no.equals(flightNumber)) {
                    fareTotal = journey.getDouble("fareTotal");
                    JSONArray bundles = journey.getJSONArray("bundles");
                    for (int l = 0; l < bundles.size(); l++) {
                        JSONObject bundle = bundles.getJSONObject(l);
                        String bundleCode = bundle.getString("bundleCode");
                        if (bundleCode.equals("EDPC") || bundleCode.equals("ESPC")) {
                            amount = bundle.getDouble("amount");
                            searchTotal = fareTotal + amount;
                        }
                    }
                }
            }
        }

        double balanceDue = selectJson.getJSONObject("bookingSummary").getDouble("balanceDue");

        writeToFile(fromCity + "/" + toCity + "/" + flight_no + "/" + fromDate + "/" + currency_code + "/" + max + "/" + fareTotal + "/" + amount + "/" + searchTotal + "/" + balanceDue + "/" + cabin,
                "/Users/<USER>/Downloads/5J行李套餐.txt");
        logger.info("行李套餐价格:" + balanceDue);
//        if (searchTotal != balanceDue) {
//            return "ERROR_EDPC价格异常";
//        }
        return "SUCCESS_EDPC价格校验成功";
    }

    private String getProxyIP(DefaultHttpClient client) {
//        String url = "http://v2.api.juliangip.com/postpay/getips?auth_info=1&auto_white=1&num=1&pt=1&result_type=text&split=1&trade_no=6475975393957960&sign=0dfadce738472ce29c42ac0436ca7f80";
        String url = "http://luluqi.user.xiecaiyun.com/api/proxies?action=getText&key=NPEEA975B7&count=100&word=&rand=false&norepeat=false&detail=false&ltime=&idshow=false";
        HttpGet httpGet = new HttpGet(url);
        try {
            HttpResponse httpResponse = client.execute(httpGet);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            String htmlContent = readHtmlContentFromEntity(httpResponse.getEntity());
            if (statusCode != 200) {
                return "ERROR_获取ip失败";
            }
            return htmlContent;
        } catch (Exception e) {
            e.printStackTrace();
            return "ERROR_获取ip异常";
        }
    }

    public synchronized void proxy_ip(DefaultHttpClient client) {
//
//        String proxyHost = "t101.juliangip.cc";
//        Integer proxyPort = 11890;
//        String proxyUser = "18201068924";
//        String proxyPass = "hhXwlqfn";

        String proxyHost = proxy_host;
        Integer proxyPort = proxy_post;
        String proxyUser = proxy_user;
        String proxyPass = proxy_pass;

//        String proxy_url = "http://" + proxyUser + ":" + proxyPass + "@" + proxyHost + ":" + proxyPort;
//        logger.info("{}本次client所用代理:" + proxy_url);

        HttpHost proxy = new HttpHost(proxyHost, proxyPort, "http");
        client.getParams().setParameter(ConnRoutePNames.DEFAULT_PROXY, proxy);
        AuthScope auth = new AuthScope(proxyHost, proxyPort);
        Credentials credentials = new org.apache.http.auth.NTCredentials(proxyUser, proxyPass, "", "");
        client.getCredentialsProvider().setCredentials(auth, credentials);
    }
}


