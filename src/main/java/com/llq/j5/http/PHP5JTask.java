package com.ota.edafare.task;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.nimbusds.jose.*;
import com.nimbusds.jose.crypto.MACSigner;
import com.ota.edafare.db.DBHelper;
import com.ota.edafare.mq.MqConsumerWrapper;
import com.ota.edafare.mq.MqProduce;
import com.ota.edafare.mq.pojo.QueueBaggageInfo;
import com.ota.edafare.obj.Flight;
import com.ota.edafare.obj.Passenger;
import com.ota.edafare.obj.ProxyIp;
import com.ota.edafare.obj.Routing;
import com.ota.edafare.service.CacheService;
import com.ota.edafare.task.util.TLSForwardUtil;
import com.ota.edafare.util.CommonUtil;
import com.ota.edafare.util.CrawlerMonitor;
import com.ota.edafare.util.DataException;
import com.ota.edafare.util.IPProxyUtil;
import com.ota.edafare.util.JRedisUtil;
import com.ota.edafare.util.httptls.HttpTls;
import com.ota.edafare.util.httptls.NewHttpTls;
import com.ota.edafare.util.httptls.SerializerUtil;
import com.ota.edafare.util.httputil.XqtHttpResponse;
import com.qunar.flight.qmonitor.QMonitor;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.Credentials;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.*;
import org.apache.http.conn.params.ConnRoutePNames;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;

public class PHP5JTask extends WrapperTask {


    static Invocable invoke = null;
    static Map<String, String> wrapperMap = new HashMap<>();

    static {
        ScriptEngineManager mgr = new ScriptEngineManager();
        ScriptEngine engine = mgr.getEngineByName("javascript");
        String path = PHP5JTask.class.getClassLoader().getResource("aes.js").getFile();
        invoke = (Invocable) engine;
        try {
            engine.eval(FileUtils.readFileToString(new File(path)));
        } catch (ScriptException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        wrapperMap.put("5j" + "domestic", "cebupacifi");
        wrapperMap.put("dg" + "domestic", "cebupacifi");
        wrapperMap.put("t6" + "domestic", "cebupacifi");
    }

    private static String OCP_APIM_SUBSCRIPTION_KEY = "07ce68d9937841a59a8156ec7dafc0b6";
    int uas = 131;
    String USER_AGENT = "Cebu Pacific/3.73.0 (com.navitaire.nps.5j; build:9; iOS 18.1.1)";
//    String USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
    public static String AUTHORIZATION_SECRET = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcHBuYW1lIjoiY2VidXBhY2lmaWMiLCJ2ZXJzaW9uIjoiMi42NC4wIiwicGxhdGZvcm0iOiJBTkRST0lEIiwiTlNUb2tlbiI6bnVsbCwidXJsIjoiaHR0cHM6Ly9tb2JpbGUtYXBpLmNlYnVwYWNpZmljYWlyLmNvbS9kb3RyZXotcHJvZC12My9hcGkvbnNrL3YxL1Rva2VuIn0.MYcI-hE5Mwu-rK3jGwQPUbiD2jB3ykaGL_XIbyrrfno";
    // 保留小数点后两位
    DecimalFormat df = new DecimalFormat("#.##");
    public static List<String> luanges = Lists.newArrayList();
    static {
        luanges.add("en;q=0.8");
        luanges.add("en-GB;q=0.7");
        luanges.add("en-US;q=0.6");
        luanges.add("de;q=0.5");
        luanges.add("ja;q=0.4");
    }

    private String http_proxys = "";

    public static String SECR = "kC2ghRh4XthMdfdY";
    public static String XAT = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb2RlVmVyc2lvbiI6Imh2dzlZcnBPTWRmenJ5RkZKZ1I5SHo0Umo3NnFoM25FIn0.FQhLXkyaetq7RZb4wIoTTdGFcHFiopsxvRsZpRlEtiU";
    public static String PARK = "VwxG&vJSrS-3*?7z";
    public static String AESK = "d15be270a756e84bae09ea88b12e80af6596541e6a5266f73a6980f1669bd718";

    private String currence = "";


    private static LinkedBlockingQueue<String> locklist = new LinkedBlockingQueue<String>();

    private static AtomicInteger requestCount = new AtomicInteger(0);
    private static AtomicInteger addonCount = new AtomicInteger(0);
    private static AtomicInteger seatCount = new AtomicInteger(0);
    private static AtomicInteger baggageCount = new AtomicInteger(0);
    private static AtomicInteger failCount = new AtomicInteger(0);

    private static AtomicInteger runCount = new AtomicInteger(0);

    private boolean hasAdministrativeFee = true;

    private String uniqueid;

    static int search_count_ceb = 0;
    static int search_count_php = 0;
    static int search_count_other = 0;

    String abck = "";
    int abck_count = 0;
    String bmsz = "";
    public  static LinkedBlockingQueue<String> cfCookie = new LinkedBlockingQueue<>();
    public  static LinkedBlockingQueue<String> cfCookiebook = new LinkedBlockingQueue<>();

    private String getcebuCookie() {

        String poll = "";
        if ((stage.equals("book") || stage.equals("order")) && site.contains("edata.onefly")) {
            logger.info("oneflybook只用一次cookie");
            poll = cfCookiebook.poll();
        } else {
            poll = cfCookie.poll();
        }
        if (StringUtils.isNotBlank(poll)) {
            logger.info("从队列获取CEB_COOKIE");
            abck = poll.split("@@@")[0];
            bmsz = poll.split("@@@")[1];
            USER_AGENT = poll.split("@@@")[2];
            abck_count = Integer.valueOf(poll.split("@@@")[3]);
            http_proxys = poll.split("@@@")[4];
            abck_count++;
            logger.info("获取Cookie队列:"+USER_AGENT+"--"+"--"+http_proxys);
            return "SUCCESS_从队列获取";
        }
        logger.info("准备实时获取CEB_COOKIE");
        Map str_body = new HashMap<>();
        str_body.put("appid", "4zlaj1ku9mfl2iva2rmqno444ej8gjjx");
        cn.hutool.http.HttpRequest body = cn.hutool.http.HttpUtil.createPost("http://api-usa.zjdanli.com/akamai/cookie/cebupacificair")
                .contentType("application/json").body(SerializerUtil.serialize(str_body));
        body.setReadTimeout(10000);
        body.setConnectionTimeout(10000);
        String s = body.execute().body();
        System.out.println("实时获取Cookie结果:"+s);
        com.alibaba.fastjson.JSONObject jsonObject = SerializerUtil.deserializeJson(s);
        if (jsonObject.toString().contains("失败")) {
            logger.info("获取丹里CEB_cookie失败");
//            return "ERROR_获取丹里CEB_cookie失败";
            throw new RuntimeException("ERROR_获取ak-Cookies失败");

        }
        String data = jsonObject.getString("data");
        USER_AGENT = jsonObject.getString("ua");
        abck = data.split("_abck=")[1].split("; bm_sz")[0];
        bmsz = data.split("bm_sz=")[1].split(",")[0];
        abck_count = 1;
        if (StringUtils.isEmpty(USER_AGENT) || StringUtils.isEmpty(abck) || StringUtils.isEmpty(bmsz)) {
            logger.info("获取ak-Cookies失败");
            throw new RuntimeException("ERROR_获取ak-Cookies失败");
        }
        return "SUCCESS_实时获取";
    }



    public void clear_add_cookie(boolean is_save) {
        if (is_save && StringUtils.isNotBlank(abck) && StringUtils.isNotBlank(bmsz) && StringUtils.isNotBlank(USER_AGENT)) {
//            if (abck_count < 4) {
                logger.info("Cookie可用;准备存进去");
                if ((stage.equals("book") || stage.equals("order")) && site.contains("edata.onefly")) {
                    cfCookiebook.offer(abck + "@@@" + bmsz + "@@@" + USER_AGENT + "@@@" + abck_count + "@@@" + http_proxys);
                } else {
                    cfCookie.offer(abck + "@@@" + bmsz + "@@@" + USER_AGENT + "@@@" + abck_count + "@@@" + http_proxys);
                }
//            }

        }
        abck = "";
        bmsz = "";
        USER_AGENT = "";
        abck_count = 0;
        setProxy();
    }


    @Override
    protected void wrapperRequest(String fromCity, String toCity, String fromDate, String retDate, List<Routing> routings) {
        uas = random.nextInt(13)+132;
        uas = 131;
//        USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/"+uas+".0.0.0 Safari/537.36";
//        USER_AGENT = "Cebu Pacific/3.68.0 (com.navitaire.nps.5j; build:2; iOS "+(random.nextInt(100)+10)+"."+random.nextInt(10)+"."+random.nextInt(10)+") Alamofire/4.9.1";
        USER_AGENT = "Cebu Pacific/3.73.1 (com.navitaire.nps.5j; build:1; iOS "+(random.nextInt(100)+10)+"."+random.nextInt(10)+"."+random.nextInt(10)+")";
        int interval_days = CommonUtil.daysBetween(CommonUtil.getCurrentTime(), fromDate);
        if (interval_days > 300 && "search".equals(stage)) {
            logger.info(fromCity + "-" + toCity + "-" + fromDate+"-"+wrapper + "-此单售卖日期超过300天;不去搜索");
            throw new DataException(CrawlerMonitor.FAIL_MESS_DATE_LIMIT);
        }

        if ("search".equals(stage)) {
            String lowprice = JRedisUtil.getKey("NODATA2" + fromCity + toCity + fromDate);
            if (StringUtils.isNotBlank(lowprice)) {
                logger.info(fromCity + "-" + toCity + "-" + fromDate + "-"+wrapper+"-"+"此单售空不去搜索");
                return;
            }
            int i = runCount.incrementAndGet();
            if(i > 10){
                runCount.decrementAndGet();
                throw new DataException(CrawlerMonitor.FAIL_MESS_CONCURRENT_LIMIT);
            }
        }

        try {
//            if(StringUtils.isNotBlank(wrapper)&&wrapper.equals("cebupacifi")){
//                search_count_ceb++;
//            }else if(StringUtils.isNotBlank(wrapper)&&wrapper.equals("php5j")){
//                search_count_php++;
//            }else{
//                search_count_other++;
//            }
//            logger.info(logId + "-开始宿务搜索咯-" + stage + "--" + search_count_ceb + "---" + search_count_php+"---"+search_count_other);

            Map<String, List<Flight>> goMap = new HashMap<>();
            Map<String, List<Flight>> rtMap = new HashMap<>();

            long mark = System.currentTimeMillis();
            List<Map<String, List<Flight>>> result;
            try {
                setProxy();
                getcebuCookie();
                result = mobileSeach(fromCity, toCity, fromDate, retDate);
            } catch (Exception exp) {
                setProxy();
                getcebuCookie();
                result = mobileSeach(fromCity, toCity, fromDate, retDate);
            }
            System.out.println("Search Time " + (System.currentTimeMillis() - mark) / 1000);

            if (result.size() > 0) {
                goMap = result.get(0);
            }

            if (result.size() > 1) {
                rtMap = result.get(1);
            }

            if ("search".equals(stage) && goMap.size() == 0) {
                Routing nullRouting = new Routing();
                nullRouting.getExtraInfo().put("nodata", "");
                routings.add(nullRouting);
//                JRedisUtil.setKey("NODATA2" + fromCity + toCity + fromDate, "1", 3 * 24 * 3600);
                return;
            }

            Iterator<String> goKey = goMap.keySet().iterator();
            Iterator<String> rtKey = rtMap.isEmpty() ? null : rtMap.keySet().iterator();

//            mark = System.currentTimeMillis();

            while (goKey.hasNext()) {
                Routing goR = new Routing();
                String go = goKey.next();
                // 0  1  2  3         4
                // i_pp_10_available_&flightKey&fareKey&currencyCode&price
                String goMoney[] = go.split("_");
                if ((int) Double.parseDouble(goMoney[1]) == 0) {
                    continue;
                }
                goR.setFromSegments(goMap.get(go));

                // 去程的单人价格和税
                goR.setAdultPrice(Double.parseDouble(goMoney[1]));
                goR.setAdultTax(Double.parseDouble(goMoney[2]));
                // 去程座位数
                goR.getExtraInfo().put("seat", goMoney[3]);

                String split[] = go.split("&");

                // set币种
//                goR.setCurrency(split[3]);

                // 去程原价
                goR.getExtraInfo().put("srcPrice", df.format(Double.parseDouble(split[4])));

                // 去程flight key
                goR.getExtraInfo().put("flight_key", split[1]);
                goR.getExtraInfo().put("fare_key", split[2]);
                goR.getExtraInfo().put("segmentKey", split[split.length-1]);

                // set 新的返程routing
                while (rtKey != null && rtKey.hasNext()) {

                    Routing rtR = new Routing();
                    String rt = rtKey.next();

                    rtR.setRetSegments(rtMap.get(rt));

                    String rtMoney[] = rt.split("_");
                    if ((int) Double.parseDouble(rtMoney[1]) == 0) {
                        continue;
                    }

                    rtR.setAdultPrice(Double.parseDouble(rtMoney[1]));
                    rtR.setAdultTax(Double.parseDouble(rtMoney[2]));
                    rtR.getExtraInfo().put("seat", rtMoney[3]);

                    split = rt.split("&");

                    rtR.getExtraInfo().put("flight_key", split[1]);
                    rtR.getExtraInfo().put("fare_key", split[2]);
                    // 回程原价
                    rtR.getExtraInfo().put("srcPrice", split[4]);
                    // 避免浅拷贝
                    addRoundTrip(goR, rtR, routings);
                }
                // reassign
                rtKey = rtMap.isEmpty() ? null : rtMap.keySet().iterator();

                // 单程情况下
                if (rtKey == null) {
                    goR.setData(CommonUtil.flightToKey(goR));
                    goR.setAdultTaxType(0);
                    goR.setApplyType(0);
                    goR.getExtraInfo().put("wrapper", "cebupacifi");
                    goR.getExtraInfo().put("currencyCode", split[3]);
                    goR.getExtraInfo().put("srcCur", split[3]);

                    // set max
                    int seat = NumberUtils.toInt(goMoney[3]);
                    if (seat > 0) {
                        if (seat > 9) {
                            seat = 9;
                        }
                        goR.setMax(seat + "");
                    }

                    if (NumberUtils.toInt(goR.getMax()) > 9) {
                        goR.setMax("9");
                    }
                    routings.add(goR);
                }
            }
            long sleep_ttt = (System.currentTimeMillis() - mark) / 1000;
            logger.info(routings.size() + "  search time  " + (System.currentTimeMillis() - mark) / 1000);

            for (Routing routing : routings) {
                if (("book".equals(stage)) && routing.getData().equals(flightData) && "1".equals(CacheService.getInstance().getSiteSysconfValue(site, "ceb_bag"))) {
                    requestCount.incrementAndGet();
                     logger.info("当前航班座位数:" + routing.getMax());
                     if( "1".equals(CommonUtil.getKey("isdea")) && NumberUtils.toInt(routing.getMax()) < 5){
                         break;
                     }
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                getBagInfo(routing,fromDate,retDate);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }).start();
                    if(sleep_ttt<12){
                        sleep_ttt = 12 - sleep_ttt;
                        logger.info("本次选座要等待 " + sleep_ttt + " S");
                        try {
                            Thread.sleep(sleep_ttt);
                        }catch (Exception e){

                        }
                    }

                }
            }

        } catch (Exception exp) {
            exp.printStackTrace();
            QMonitor.recordOne("CEB_EXP");
            throw exp;
        } finally {
            if ("search".equalsIgnoreCase(stage)) {
                runCount.decrementAndGet();
            }

            if (routings == null || routings.size() < 1) {
                logger.info(fromCity + "--" + toCity + "--" + fromDate + "--" + wrapper + "-此单宿务是售空");
            } else if (routings != null) {
                logger
                    .info(fromCity + "--" + toCity + "--" + fromDate + "--" + wrapper + "-此单宿务航班数量:" + routings.size());
            }
        }
    }


    public String getSeatMap(String ow_key, String rt_key, String token, String aes_auth,String segmentKey) {
        String ow = ow_key.split("&")[0];
        JSONArray jsonArray = new JSONArray();

        String[] split = segmentKey.split("#");
        for (int i = 0; i < split.length; i++) {
            jsonArray.put(split[i]);
        }
        if (StringUtils.isNotBlank(rt_key)) {
            String rt = rt_key.split("&")[0];
            jsonArray.put(rt);
        }
//        logger.info("本次获取航班座位分布参数:{}" + jsonArray.toString());
        String action = "seatmaps";
//        String req_url = "https://soar.cebupacificair.com/ceb-omnix_proxy?content=";
//        String seat_map = sendsslpostParam(client, req_url, action, jsonArray.toString(), "获取所有座位分布", order_no);
        String time = System.currentTimeMillis() + "";
        Map<String, String> authMap = sendPostSO(action, jsonArray.toString(), token, aes_auth, "查询座位");
        String content = authMap.get("content");
        return content;
    }

//    private void getBagInfo(Routing routing) {
//        try {
//
//            //去数据库查询是否有行李数据
//            List<Flight> fromSegments = routing.getFromSegments();
//            String dep = fromSegments.get(0).getDepAirport();
//            String arr = fromSegments.get(fromSegments.size() - 1).getArrAirport();
//            String carrier = fromSegments.get(0).getCarrier().toLowerCase();
//            List<Map<String,Object>> bags = DBHelper.getInstance().getDataMap("select * from baggage where wrapper = '"+carrier+"' and fromCity = ? and toCity = ? ", new Object[]{dep,arr},DBHelper.DEFAULT);
//
//            if(bags.size() > 0){
//                return;
//            }
//
//            String url = "https://soar.cebupacificair.com/ceb-omnix_proxy";
//
//            Map<String, String> getauth = sendPostSO("accessToken", "", "", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb21wYW55IjoiQ0VCVSBBaXIgSW5jLiIsIm5hbWUiOiJvbW5pWCIsInZlcnNpb24iOjEsImNvZGVWZXJzaW9uIjoiUHRFRHNWNVA1QUhMb0FoWnk3eHE1SW5XbGZOTW9WaDkifQ.rJdOfwQPOLGObQUkZOX0eEfpXmqHtAkeXNLjorQvQj4", "");
//            logger.info("CEB获取Auth结果:"+getauth.get("code"));
//            String result = getauth.get("content");
//            logger.info("CEB获取AuthResult:"+result);
//            String aes_auth = JSONObject.fromObject(result).getString("Authorization");
//            String token = JSONObject.fromObject(result).getString("X-Auth-Token");
//            System.out.println("==》"+token);
//
//            if(StringUtils.isBlank(token)){
//                return;
//            }
//            String go_flight_key = routing.getExtraInfo().containsKey("flight_key") ? routing.getExtraInfo().get("flight_key") : routing.getExtraInfo().get("go_flight_key");
//            String go_fare_key = routing.getExtraInfo().containsKey("fare_key") ? routing.getExtraInfo().get("fare_key") : routing.getExtraInfo().get("go_fare_key");
//            String currencyCode = routing.getExtraInfo().get("currencyCode");
//
//            String ow_key = go_flight_key + "&" + go_fare_key + "&" + 0 + "&" + routing.getMax() + "&" + currencyCode;
//            String re_flight_key = routing.getExtraInfo().get("re_flight_key");
//            String re_fare_key = routing.getExtraInfo().get("re_fare_key");
//
//            String rt_key = (StringUtils.isBlank(re_fare_key) || StringUtils.isBlank(re_flight_key)) ? "" : re_flight_key + "&" + re_fare_key + "&" + 0 + "&" + routing.getMax() + "&" + currencyCode;
//
//            List<String> keys = Lists.newArrayList();
//            keys.add(ow_key);
//            if (StringUtils.isNotBlank(rt_key)) {
//                keys.add(rt_key);
//            }
//            String select_count =  selectFlights( keys, 1, 0, currencyCode, token , aes_auth);
//            List<String> pass_keys = Lists.newArrayList();
//            try {
//                JSONObject jsonObject = JSONObject.fromObject(select_count);
//                String authorization = jsonObject.getString("Authorization");
//                if (StringUtils.isNotBlank(authorization)) {
//                    JSONArray passengers = jsonObject.getJSONArray("passengers");
//                    for (int i = 0; i < passengers.length(); i++) {
//                        pass_keys.add(passengers.getJSONObject(i).getString("passengerTypeCode") + "&" + passengers.getJSONObject(i).getString("passengerKey"));
//                    }
//                } else {
//                    logger.info("选择航班失败;没获取到新的aes");
//                    return ;
//                }
//            } catch (Exception e) {
//                logger.error( e.getMessage(), e);
//                return ;
//            }
//            List<Passenger> passengers = new ArrayList<>();
//            Passenger passenger = new Passenger();
//            passenger.setName(getChar(3) +  "/"  + getChar(3));
//            passenger.setBirthday("19871111");
//            passenger.setAgeType(0);
//            passenger.setGender("M");
//            passenger.setNationality("US");
//            passengers.add(passenger);
//
//            String add_pass_count = addPassenger( passengers, pass_keys, token, aes_auth);
//
//            JSONObject jsonObject = JSONObject.fromObject(add_pass_count);
//            JSONArray jsonArray = jsonObject.getJSONObject("addOns").getJSONArray("baggage").getJSONObject(0).getJSONArray("ssrs");
//
//            String depDate = "";
//            String flightNo = "";
//            int pc = 1;
//
//            List<Object[]> parameter = new ArrayList<>();
//            for (int i = 0; i < jsonArray.length(); i++) {
//                JSONObject sssJson = jsonArray.getJSONObject(i);
//                double price = sssJson.getDouble("price");
//                String ssrCode = sssJson.getString("ssrCode");
//                String weight = ssrCode.replaceAll("BG", "");
//                System.out.println(weight);
//                double bagprice = new BigDecimal(price).setScale(2,   BigDecimal.ROUND_HALF_UP).doubleValue();
//                String outId = CommonUtil.NEWTFLUG + carrier + dep + arr + depDate + flightNo + pc + weight;
//                Object[] para = new Object[]{outId,carrier,depDate,dep,arr,flightNo,pc,weight,bagprice,currencyCode};
//                parameter.add(para);
//            }
//            DBHelper.getInstance().updateBatchSql("replace into baggage (outId,wrapper,depDate,fromCity,toCity,flightNo,pc,weight,price,cur,returnType) values (?,?,?,?,?,?,?,?,?,?,0) ", parameter, DBHelper.DEFAULT);
//
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }


    /**
     * 搜索行李价格
     *
     * @param routing
     */

    public void getBagInfo(Routing routing,String fromDate, String retDate) {

        try {
            addonCount.incrementAndGet();

            setProxy();
            getcebuCookie();
            //去数据库查询是否有行李数据
            List<Flight> fromSegments = routing.getFromSegments();
            String dep = fromSegments.get(0).getDepAirport();
            String arr = fromSegments.get(fromSegments.size() - 1).getArrAirport();
            String carrier = fromSegments.get(0).getCarrier().toLowerCase();
            // String total_token = locklist.poll();

            String aes_auth = "";
            String token = "";
            // if (StringUtils.isBlank(total_token) || StringUtils.isBlank(total_token)) {
                String timestamp1 = rand();
                Map<String, String> getauth = new HashMap<>();
                String code = "";
                for (int i = 0; i < 3; i++) {
                    getauth = sendPostSO("v2/accessToken", "", "&" + timestamp1,
                        XAT,
                        "accessToken");
                    code = getauth.get("code");
                    if (code.equals("200")) {
                        break;
                    }
                }
                if (!code.equals("200")) {
                    failCount.incrementAndGet();
                    return;
                }
                
                // logger.info(logId+"--"+"CEB获取Auth结果:" + getauth.get("code"));
                String result = getauth.get("content");
                // logger.info(logId+"--"+"CEB获取AuthResult:" + result);
                aes_auth = JSONObject.fromObject(result).getString("Authorization");
                token = JSONObject.fromObject(result).getString("X-Auth-Token") + "&" + timestamp1;
                // total_token = token + "@@@" + aes_auth;
            // } else {
            //     // System.out.println("取token");
            //     token = total_token.split("@@@")[0];
            //     aes_auth = total_token.split("@@@")[1];
            // }

            if (StringUtils.isBlank(token)) {
                failCount.incrementAndGet();
                return;
            }

            String go_flight_key = routing.getExtraInfo().containsKey("flight_key") ? routing.getExtraInfo().get("flight_key") : routing.getExtraInfo().get("go_flight_key");
            String go_fare_key = routing.getExtraInfo().containsKey("fare_key") ? routing.getExtraInfo().get("fare_key") : routing.getExtraInfo().get("go_fare_key");
            String currencyCode = routing.getExtraInfo().get("currencyCode");
            String segmentKey = routing.getExtraInfo().get("segmentKey");
            String ow_key = go_flight_key + "&" + go_fare_key + "&" + 0 + "&" + routing.getMax() + "&" + currencyCode;
            String re_flight_key = routing.getExtraInfo().get("re_flight_key");
            String re_fare_key = routing.getExtraInfo().get("re_fare_key");
            String rt_key = (StringUtils.isBlank(re_fare_key) || StringUtils.isBlank(re_flight_key)) ? "" : re_flight_key + "&" + re_fare_key + "&" + 0 + "&" + routing.getMax() + "&" + currencyCode;

            List<String> keys = Lists.newArrayList();
            keys.add(ow_key);
            if (StringUtils.isNotBlank(rt_key)) {
                keys.add(rt_key);
            }

            String select_count = selectFlights(keys, 1, 0, currencyCode, token, aes_auth);

            JSONObject jsonObject = JSONObject.fromObject(select_count);
            JSONArray baggage = jsonObject.getJSONObject("availableSsrs").getJSONArray("journeySsrs");
            JSONArray goSsrs = baggage.getJSONObject(0).getJSONArray("ssrs");
            JSONArray reSsrs = baggage.length() > 1 ? baggage.getJSONObject(1).getJSONArray("ssrs") : null;

            String depDate = fromDate.replaceAll("\\-", "");
            String flightNo = CommonUtil.flightToKey(routing.getFromSegments());
            String cabin = CommonUtil.flightToCabinKey(routing.getFromSegments());
            // 去程行李update
            updateBaggage(carrier, dep, arr, depDate, flightNo, currencyCode, goSsrs, cabin);
            baggageCount.incrementAndGet();

            // 回程行李update
            if (reSsrs != null) {
                depDate = retDate.replaceAll("\\-", "");
                flightNo = CommonUtil.flightToKey(routing.getRetSegments());
                cabin = CommonUtil.flightToCabinKey(routing.getRetSegments());
                updateBaggage(carrier, arr, dep, depDate, flightNo, currencyCode, reSsrs, cabin);
            }

            String seatMap = getSeatMap(ow_key, rt_key, token, aes_auth,segmentKey);
            seatInfo(seatMap, routing, currencyCode,fromDate);
            seatCount.incrementAndGet();
            clear_add_cookie(true);
        } catch (Exception e) {
            failCount.incrementAndGet();
            logger.error("航线>>" + routing.getData() + ">>抓取行李价格异常>>" + e.getMessage());
            e.printStackTrace();
        } finally {
            logger.info(
                "book的统计计数：requestCount=" + requestCount.get() + ", addonCount=" + addonCount.get() + ", seatCount="
                    + seatCount.get() + ", baggageCount=" + baggageCount.get() + ", failCount=" + failCount.get());
        }
    }

    private void seatInfo(String seatMap, Routing routing, String currencyCode,String fromDate) {
        JSONArray flightList = JSONArray.fromString(seatMap);
        JSONObject flight = new JSONObject();
        for (int i = 0; i < flightList.length(); i++) {
            JSONObject jsonObject = new JSONObject();
            List<Flight> fromSegments = routing.getFromSegments();

            JSONObject seatJSON = flightList.getJSONObject(i);
            String departureStation = seatJSON.getString("departureStation");
            String arrivalStation = seatJSON.getString("arrivalStation");

            //获取当前出发到达的航班号
            String flightNumber = "";
            for (Flight fromSegment : fromSegments) {
                String depAirport = fromSegment.getDepAirport();
                String arrAirport = fromSegment.getArrAirport();
                if (depAirport.equals(departureStation) && arrAirport.equals(arrivalStation)) {
                    flightNumber = fromSegment.getFlightNumber();
                }
            }


            String planeType = seatJSON.getString("name").split(" ")[0];
            jsonObject.put("planeType", planeType);
            JSONArray units = seatJSON.getJSONArray("units");
            JSONArray availableSeats = new JSONArray();
            JSONArray unavailableSeats = new JSONArray();
            JSONArray unavailableSeatInfo = new JSONArray();
            JSONArray groupTypes = new JSONArray();

            //group价格整理
            JSONObject groupJSON = new JSONObject();
            int count = 1;
            for (int j = 0; j < units.length(); j++) {
                JSONObject group = new JSONObject();
                JSONObject availableSeat = new JSONObject();
                JSONObject unit = units.getJSONObject(j);
                String designator = unit.getString("designator");
                String properties = unit.getJSONArray("properties").toString();
                String availability = unit.getString("availability");
                String amount = unit.getString("amount");
                if (availability.equals("Open")) {
                    availableSeat.put("aisle", properties.contains("AISLE"));
                    availableSeat.put("childAllowed", true);
                    availableSeat.put("designator", designator);
                    availableSeat.put("emergencyExit", properties.contains("EXITROW"));
                    availableSeat.put("extraLegRoom", properties.contains("LEGROOM"));
                    if (!groupJSON.has(amount)) {
                        availableSeat.put("group", count + "");
                        group.put("srcCurrency", currencyCode);
                        group.put("group", count+"");
                        group.put("srcPrice", amount);
                        groupTypes.put(group);
                        groupJSON.put(amount, count + "");
                        count++;
                    } else {
                        String groupCount = groupJSON.getString(amount);
                        availableSeat.put("group", groupCount);
                    }
                    availableSeat.put("infantAllowed", "");
                    availableSeat.put("upFront", "false");
                    availableSeat.put("windowSeat", properties.contains("WINDOW"));
                    availableSeats.put(availableSeat);
                } else {
                    availableSeat.put("aisle", properties.contains("AISLE"));
                    availableSeat.put("childAllowed", "");
                    availableSeat.put("designator", designator);
                    availableSeat.put("emergencyExit", properties.contains("EXITROW"));
                    availableSeat.put("extraLegRoom", properties.contains("LEGROOM"));
                    availableSeat.put("group", "");
                    availableSeat.put("infantAllowed", true);
                    availableSeat.put("upFront", "false");
                    availableSeat.put("windowSeat", properties.contains("WINDOW"));
                    unavailableSeatInfo.put(availableSeat);
                    unavailableSeats.put(designator);
                }
            }
            jsonObject.put("availableSeat", availableSeats);
            jsonObject.put("groupType", groupTypes);
            jsonObject.put("unavailableSeat", unavailableSeats);
            jsonObject.put("unavailableSeatInfo", unavailableSeatInfo);
            flight.put(flightNumber, jsonObject);
        }
        DBHelper.getInstance().updateSql("replace into bk_seat_new (fromCity,toCity,depDate,flightNo,info,updateTime,returnType) values (?,?,?,?,?,now(),0)", new Object[]{routing.getFromSegments().get(0).getDepAirport(),routing.getFromSegments().get(routing.getFromSegments().size()-1).getArrAirport(),fromDate,routing.getData(),flight.toString()}, DBHelper.DEFAULT);
//        System.out.println("座位JSON"+flight);
    }


    /**
     * 更新航程不同规格的行李价格
     */
    private void updateBaggage(String carrier, String dep, String arr, String depDate, String flightNo,
        String currencyCode, JSONArray ssrs, String cabin) {

        String domesticCarrier = carrier.toLowerCase();
        String overseasCarrier = domesticCarrier;

        String domesticCarrierKey = domesticCarrier + "domestic";
        if (wrapperMap.containsKey(domesticCarrierKey)) {
            domesticCarrier = wrapperMap.get(domesticCarrierKey);
        }
        
        int PC20 = 0;
        int P20B = 0;
        int P20C = 0;
        int PC32 = 0;
        int EX4 = 0;
        int EX8 = 0;
        int EX12 = 0;

        for (int i = ssrs.length() - 1; i >= 0; i--) {
            JSONObject ssr = (JSONObject)ssrs.get(i);

            if ("PC20".equals(ssr.getString("ssrCode")) || "PC20".equals(ssr.getString("feeCode"))) {
                PC20 =
                    ssr.getJSONArray("passengersAvailability").getJSONObject(0).getJSONObject("value").getInt("price");
            }

            if ("P20B".equals(ssr.getString("ssrCode")) || "P20B".equals(ssr.getString("feeCode"))) {
                P20B =
                    ssr.getJSONArray("passengersAvailability").getJSONObject(0).getJSONObject("value").getInt("price");
            }

            if ("P20C".equals(ssr.getString("ssrCode")) || "P20C".equals(ssr.getString("feeCode"))) {
                P20C =
                    ssr.getJSONArray("passengersAvailability").getJSONObject(0).getJSONObject("value").getInt("price");
            }

            if ("PC32".equals(ssr.getString("ssrCode")) || "PC32".equals(ssr.getString("feeCode"))) {
                PC32 =
                    ssr.getJSONArray("passengersAvailability").getJSONObject(0).getJSONObject("value").getInt("price");
            }

            if ("4EX".equals(ssr.getString("ssrCode")) || "4EX".equals(ssr.getString("feeCode"))) {
                EX4 =
                    ssr.getJSONArray("passengersAvailability").getJSONObject(0).getJSONObject("value").getInt("price");
            }

            if ("8EX".equals(ssr.getString("ssrCode")) || "8EX".equals(ssr.getString("feeCode"))) {
                EX8 =
                        ssr.getJSONArray("passengersAvailability").getJSONObject(0).getJSONObject("value").getInt("price");
            }

            if ("12EX".equals(ssr.getString("ssrCode")) || "12EX".equals(ssr.getString("feeCode"))) {
                EX12 =
                    ssr.getJSONArray("passengersAvailability").getJSONObject(0).getJSONObject("value").getInt("price");
            }
        }

        List<Object[]> parameterFor5j = new ArrayList<>();
        List<Object[]> parameterForCebupacifi = new ArrayList<>();
        List<QueueBaggageInfo> baggageInfosForCebupacifi = Lists.newArrayList();
        for (int pc = 1; pc <= 3; pc++) {
            for (int weight = 20; weight <= 32; weight += 4) {
                int price = 0;
                if (pc == 1) {
                    if (weight == 20) {
                        if (PC20 == 0) {
                            continue;
                        }
                        price = PC20;
                    } else if (weight == 24) {
                        if (PC20 == 0 || EX4 == 0) {
                            continue;
                        }
                        price = PC20 + EX4;
                    } else if (weight == 28) {
                        if (PC32 == 0) {
                            continue;
                        }
                        price = PC32;
                    } else {
                        if (PC32 == 0) {
                            continue;
                        }
                        price = PC32;
                    }
                } else if (pc == 2) {
                    if (weight == 20) {
                        if (PC20 == 0 || P20B == 0) {
                            continue;
                        }
                        price = PC20 + P20B;
                    } else if (weight == 24) {
                        if (PC20 == 0 || P20B == 0 || EX4 == 0) {
                            continue;
                        }
                        price = PC20 + P20B + pc * EX4;
                    } else if (weight == 28) {
//                        if (PC32 == 0) {
//                            continue;
//                        }
//                        price = pc * PC32;
                        if (PC20 == 0 || P20B == 0 || EX8 == 0) {
                            continue;
                        }
                        price = PC20 + P20B + pc * EX8;
                    } else {
//                        if (PC32 == 0) {
//                            continue;
//                        }
//                        price = pc * PC32;
                        if (PC20 == 0 || P20B == 0 || EX12 == 0) {
                            continue;
                        }
                        price = PC20 + P20B + pc * EX12;
                    }
                } else if (pc == 3) {
                    if (weight == 20) {
                        if (PC20 == 0 || P20B == 0 || P20C == 0) {
                            continue;
                        }
                        price = PC20 + P20B + P20C;
                    } else if (weight == 32) {
                        if (PC20 == 0 || P20B == 0 || P20C == 0 || EX12 == 0) {
                            continue;
                        }
                        price = PC20 + P20B + P20C + pc * EX12;
                    } else {
                        continue;
                    }
                }

                String outId = "F" + overseasCarrier + dep + arr + pc + (pc * weight);
                Object[] para =
                    new Object[] {outId, overseasCarrier, "", dep, arr, "", pc, (pc * weight), price, currencyCode};
                parameterFor5j.add(para);
                String outId2 = "TF" + domesticCarrier + dep + arr + pc + (pc * weight);
                Object[] para2 =
                    new Object[] {outId2, domesticCarrier, "", dep, arr, "", pc, (pc * weight), price, currencyCode};
                parameterForCebupacifi.add(para2);
                QueueBaggageInfo baggageInfoForCebupacifi = new QueueBaggageInfo();
                baggageInfoForCebupacifi.setCur(currencyCode);
                baggageInfoForCebupacifi.setFlightNo(flightNo);
                baggageInfoForCebupacifi.setPc(pc+"");
                baggageInfoForCebupacifi.setPrice(price);
                baggageInfoForCebupacifi.setFromCity(dep);
                baggageInfoForCebupacifi.setToCity(arr);
                baggageInfoForCebupacifi.setWeight(pc*weight);
                baggageInfoForCebupacifi.setWrapper(domesticCarrier);
                baggageInfoForCebupacifi.setDepDate(depDate);
                baggageInfoForCebupacifi.setCabin(cabin);
                baggageInfoForCebupacifi.setTimestamp(System.currentTimeMillis() + "");
                baggageInfosForCebupacifi.add(baggageInfoForCebupacifi);
                System.out.printf("航线:%s>>%dx%dkg行李入库参数>>%s", dep + "-" + arr, pc, weight, Arrays.toString(para));
                // System.out.println();
            }
        }
        // update batch
        DBHelper.getInstance().updateBatchSql(
            "replace into baggage (outId,wrapper,depDate,fromCity,toCity,flightNo,pc,weight,price,cur,returnType) values (?,?,?,?,?,?,?,?,?,?,0) ",
            parameterFor5j, DBHelper.DEFAULT);
        DBHelper.getInstance().updateBatchSql(
            "replace into baggage_new_ctrip (outId,wrapper,depDate,fromCity,toCity,flightNo,pc,weight,price,cur,isFlag) values (?,?,?,?,?,?,?,?,?,?,0) ",
            parameterForCebupacifi, DBHelper.DEFAULT);

        try {
            if(CollectionUtils.isNotEmpty(baggageInfosForCebupacifi)){
                MqConsumerWrapper.sendMqworker.execute(() -> MqProduce.send(JSON.toJSONString(baggageInfosForCebupacifi), MqProduce.XQT_BAGGAGE_QUEUE));
                logger.info("send mq ok");
            }
        }catch (Exception e){
            logger.info("send mq error",e);
        }
    }

    public String selectFlights(List<String> keys, int adu, int chd, String cur, String token, String aes_auth) {
        String action = "v3/trip";
        String nonce = System.currentTimeMillis() + "";
        String hashString = uniqueid + "/" + action + nonce + aes_auth;

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("hash", generateHMACForSearch(hashString));
        jsonObject.put("nonce", nonce);
        JSONObject infantCount = new JSONObject();
        infantCount.put("seat", "0");
        infantCount.put("lap", "0");
        jsonObject.put("infantCount", infantCount);
        jsonObject.put("promoCode", "");
        jsonObject.put("adultCount", adu);
        JSONArray jsonArray = new JSONArray();
        jsonArray.put("MAFI");
        jsonObject.put("ssrs", jsonArray);
        jsonObject.put("childCount", chd);
        jsonObject.put("currency", cur);
        JSONArray routes = new JSONArray();
        for (int i = 0; i < keys.size(); i++) {
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("fareAvailabilityKey", keys.get(i).split("&")[1]);
            jsonObject1.put("journeyKey", keys.get(i).split("&")[0]);
            routes.put(jsonObject1);
        }
        jsonObject.put("routes", routes);
        Map<String, String> select_result = sendPostSO(action, jsonObject.toString(), token, aes_auth, "select");
//        sendsslpostParam(client, req_url, action, jsonObject.toString(), "选择航班", token, aes_auth);
        return select_result.get("content");
    }

    public String addPassenger(List<Passenger> passengers, List<String> pass_keys, String token, String aes_auth) {

        String action = "guestdetails";
        String nonce = System.currentTimeMillis() + "";
        String hashString = uniqueid + "/" + action + nonce + aes_auth;

        // String req_url = "https://soar.cebupacificair.com/ceb-omnix_proxy?content=";
        String c_title = "MS";
        if (passengers.get(0).getGender().equals("M")) {
            c_title = "MR";
        }
        String passenger_params = "";
        if (pass_keys.size() != passengers.size()) {
            logger.info(logId+"--"+"{}添加乘客失败;乘客key不足");
            return "ERROR_添加乘客失败;乘客key不足";
        }
        for (int i = 0; i < passengers.size(); i++) {
            int ageType = passengers.get(i).getAgeType();
            String pass_key = "";
            if (ageType == 0) {
                for (int i1 = 0; i1 < pass_keys.size(); i1++) {
                    String passs_type = pass_keys.get(i1).split("&")[0];
                    if (passs_type.equals("ADT")) {
                        pass_key = pass_keys.get(i1).split("&")[1];
                        pass_keys.remove(pass_keys.get(i1));
                        break;
                    }

                }
            }
            if (ageType == 1) {
                for (int i1 = 0; i1 < pass_keys.size(); i1++) {
                    String passs_type = pass_keys.get(i1).split("&")[0];
                    if (passs_type.equals("CHD")) {
                        pass_key = pass_keys.get(i1).split("&")[1];
                        pass_keys.remove(pass_keys.get(i1));
                        break;
                    }
                }
            }
            if (StringUtils.isBlank(pass_key)) {
                logger.info(logId+"--"+"{}获取的乘客KEY为空;结束");
                return "ERROR_获取的乘客KEY为空;不继续";
            }
            String title = "MS";
            String gender = "Female";
            if (passengers.get(i).getGender().equals("M")) {
                title = "MR";
                gender = "Male";
            }
            String birthday = passengers.get(i).getBirthday();
            birthday = birthday.split("T")[0];
            passenger_params += "{" +
                    "  \"isInfant\" : false," +
                    "  \"ssrs\" : [" +
                    "  ]," +
                    "  \"travelDocuments\" : [" +
                    "  ]," +
                    "  \"passengerKey\" : \"" + pass_key + "\"," +
                    "  \"name\" : {" +
                    "    \"middle\" : \"\"," +
                    "    \"first\" : \"" + passengers.get(i).getName().split("/")[1] + "\"," +
                    "    \"last\" : \"" + passengers.get(i).getName().split("/")[0] + "\"," +
                    "    \"suffix\" : \"\"," +
                    "    \"title\" : \"" + title + "\"" +
                    "  }," +
                    "  \"info\" : {" +
                    "    \"nationality\" : \"" + passengers.get(i).getNationality() + "\"," +
                    "    \"residentCountry\" : \"" + passengers.get(i).getNationality() + "\"," +
                    "    \"gender\" : \"" + gender + "\"," +
                    "    \"dateOfBirth\" : \"" + birthday.substring(0, 4) + "-" + birthday.substring(4, 6) + "-" + birthday.substring(6, 8) + "\"" +
                    "  }" +
                    "}";
            if (i != passengers.size() - 1) {
                passenger_params = passenger_params + ",";
            }
        }
        String phone_num = getPhoneNo();
        String email = phone_num + "@163.com";
        String params = "{" +
                "  \"hash\": " + "\"" + generateHMACForSearch(hashString) + "\"," +
                "  \"nonce\": " + "\"" + nonce + "\"," +
                "  \"contacts\" : [" +
                "    {" +
                "      \"contactTypeCode\" : \"P\"," +
                "      \"emailAddress\" : \"" + email + "\"," +
                "      \"name\" : {" +
                "        \"last\" : \"" + passengers.get(0).getName().split("/")[0] + "\"," +
                "        \"title\" : \"" + c_title + "\"," +
                "        \"suffix\" : \"\"," +
                "        \"middle\" : \"\"," +
                "        \"first\" : \"" + passengers.get(0).getName().split("/")[1] + "\"" +
                "      }," +
                "      \"phoneNumbers\" : [" +
                "        {" +
                "          \"type\" : \"Home\"," +
                "          \"number\" : \"" + phone_num + "\"" +
                "        }," +
                "        {" +
                "          \"type\" : \"Other\"," +
                "          \"number\" : \"" + phone_num + "\"" +
                "        }" +
                "      ]" +
                "    }" +
                "  ]," +
                "  \"passengers\" : [" + passenger_params +
                "  ]" +
                "}";
        Map<String, String> addPass_Result = sendPostSO(action, params, token, aes_auth, "add_pass");
//        String addpass_count = sendsslpostParam(client, req_url, action, params, "添加乘客", token, aes_auth);
        String addpass_count = addPass_Result.get("content");
        System.out.println(addpass_count);
        return addpass_count;
    }

    public void addRoundTrip(Routing goR, Routing rtR, List<Routing> routings) {
        Routing routing = new Routing();

        // routine
        routing.setAdultTaxType(0);
        routing.setApplyType(0);

        routing.setFromSegments(goR.getFromSegments());
        routing.setRetSegments(rtR.getRetSegments());

        routing.getExtraInfo().put("wrapper", "cebupacifi");
        routing.getExtraInfo().put("currencyCode", goR.getCurrency());
        routing.getExtraInfo().put("srcCur", goR.getCurrency());
        // 去程原价
        String goSrcPrice = goR.getExtraInfo().get("srcPrice");
        // 回程原价
        String rtSrcPrice = rtR.getExtraInfo().get("srcPrice");
        // 保留后两位相加
        routing.getExtraInfo().put("srcPrice", df.format(Double.parseDouble(goSrcPrice) + Double.parseDouble(rtSrcPrice)));

        // 去程flight key
        routing.getExtraInfo().put("go_flight_key", goR.getExtraInfo().get("flight_key"));
        routing.getExtraInfo().put("go_fare_key", goR.getExtraInfo().get("fare_key"));
        // 回程flight key
        routing.getExtraInfo().put("re_flight_key", rtR.getExtraInfo().get("flight_key"));
        routing.getExtraInfo().put("re_fare_key", rtR.getExtraInfo().get("fare_key"));

        // 拼接航班号
        routing.setData(CommonUtil.flightToKey(routing));

        // 单人价格 相加
        routing.setAdultPrice(goR.getAdultPrice() + rtR.getAdultPrice());
        // 单人税 相加
        routing.setAdultTax(goR.getAdultTax() + rtR.getAdultTax());
        // 剩余座位数 取较小值
        String goSeat = goR.getExtraInfo().get("seat");
        String rtSeat = rtR.getExtraInfo().get("seat");
        int seat = Math.min(NumberUtils.toInt(goSeat), NumberUtils.toInt(rtSeat));
        routing.getExtraInfo().put("seat", String.valueOf(seat));

        // set max
        if (seat > 0) {
            if (seat > 7) {
                seat = 7;
            }
            routing.setMax(seat + "");
        }

        if (NumberUtils.toInt(rtR.getMax()) > 4) {
            routing.setMax("4");
        }
        // add
        routings.add(routing);
    }

    public static String getChar(int length) {
        char[] ss = new char[length];
        int i = 0;
        while (i < length) {

            ss[i] = (char) ('A' + Math.random() * 26);

            i++;
        }
        String str = new String(ss);
        return str;
    }

    public static String getPhoneNo() {
        Random random = new Random();
        String[] titles = {"176", "131", "156", "137", "132", "188", "155", "136", "133", "130", "132", "185", "186", "151", "152", "189", "175"};
        int index = random.nextInt(titles.length);
        String title = titles[index];

        int one = random.nextInt(10);
        int two = random.nextInt(10);
        int three = random.nextInt(10);
        int forn = random.nextInt(10);
        String middle = one + "" + two + three + forn;

        int one1 = random.nextInt(10);
        int two1 = random.nextInt(10);
        int three1 = random.nextInt(10);
        int forn1 = random.nextInt(10);
        String last = one1 + "" + two1 + three1 + forn1;
        String phone = title + middle + last;
        return phone;
    }

    public String sendsslpostParam(DefaultHttpClient client, String req_url, String action, String param, String tag, String token, String aes_auth) {
        try {
            action = (String) invoke.invokeFunction("encrypt", action, aes_auth.toString() + token + PARK);
            action = action.substring(0, 40) + aes_auth + token + action.substring(40);
            param = (String) invoke.invokeFunction("encrypt", param, aes_auth.toString() + token + PARK);
            param = param.substring(0, 40) + aes_auth + token + param.substring(40);

            HttpPost httpPost = new HttpPost(req_url.split("\\?")[0]);
            httpPost.setHeader("X-Auth-Token", "" + token);
            httpPost.setHeader("Referer", "https://www.cebupacificair.com");
            httpPost.setHeader("Origin", "https://www.cebupacificair.com");
            httpPost.addHeader("Accept-Encoding", "gzip, deflate, br");
            httpPost.setHeader("content", action);
            httpPost.setHeader("scope", "omnix");
            if (tag.equals("登陆")) {
                httpPost.setHeader("uniqueid", "Qon2Gj2N6Mneim");
            }
            httpPost.setHeader("User-Agent", USER_AGENT);
            httpPost.setHeader("Authorization", "Bearer " + aes_auth);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("content", param);
            httpPost.setEntity(new StringEntity(jsonObject.toString(), "UTF-8"));
            HttpResponse execute = client.execute(httpPost);
            logger.info(logId+"--"+"{}{}本次请求的Response {}" + execute);
            String html = readHtmlContentFromEntity(execute.getEntity());
            if (execute.getStatusLine().getStatusCode() != 200) {
                System.out.println(html);
            }
            return html;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    public List<Map<String, List<Flight>>>  mobileSeach(String fromCity, String toCity, String fromDate, String retDate) {

        //手机价格 是没有 手续费得，但是有得航班必须加手续费 才是官网价格
        List<Map<String, List<Flight>>> rtn = new ArrayList<>();
        String ip = "";
        Map<String, String> re = null;
        String ip_rand = "";
        try {
            String token = "";
            String aes_auth = "";
            String total_token = "";
            if(stage.equals("search")){
                total_token = locklist.poll();
            }
            try {
                if (StringUtils.isBlank(total_token) || StringUtils.isBlank(total_token)) {
                    String timestamp1 = rand();
                    Map<String, String> getauth = sendPostSO("v2/accessToken", "", "&"+timestamp1, XAT, "accessToken");
                    String code = getauth.get("code");
                    for (int i = 0; i < 3; i++) {
                        if (!code.equals("200")){
                            getauth = sendPostSO("v2/accessToken", "", "&"+timestamp1, XAT, "accessToken");
                            code = getauth.get("code");
                        }

                    }
                    logger.info(logId+"--"+"CEB获取Auth结果:" + getauth.get("code"));
                    String result = getauth.get("content");
                    logger.info(logId+"--"+"CEB获取AuthResult:" + result);
                    aes_auth = JSONObject.fromObject(result).getString("Authorization");
                    token = JSONObject.fromObject(result).getString("X-Auth-Token") + "&" + timestamp1;
                    total_token = token + "@@@" + aes_auth + "@@@" + http_proxys+"@@@"+uniqueid;
//                    System.out.println("==》" + token);
//                    System.out.println("==》" + aes_auth);
//                    if (StringUtils.isNotBlank(token)) {
//                        JRedisUtil.setKey("PHP5A_SEARCH_TOKE", token, 5);
//                        JRedisUtil.setKey("PHP5A_SEARCH_AUTH", aes_auth, 5);
//                    }
                }else {
                    System.out.println("取token");
                    token = total_token.split("@@@")[0];
                    aes_auth = total_token.split("@@@")[1];
                    http_proxys = total_token.split("@@@")[2];
                    uniqueid = total_token.split("@@@")[3];
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            if (StringUtils.isBlank(aes_auth) || StringUtils.isBlank(token)) {
                throw new DataException();
            }

            String cur = isCur(fromCity);
            if(cur.equalsIgnoreCase("HKD")){
                hasAdministrativeFee = false;
            }

            String nonce = System.currentTimeMillis() + "";
            String hashString = uniqueid + "/availability" + nonce + aes_auth;
            String params = "{" +
                    "  \"lffMode\": false," +
                    "  \"rebook\": false," +
                    "  \"hash\": " + "\"" + generateHMACForSearch(hashString) + "\"," +
                    "  \"nonce\": " + "\"" + nonce + "\"," +
                    (!hasAdministrativeFee ? "  \"ssrs\": []," : "  \"ssrs\": [" + "    \"MAFI\"" + "  ],") +
                    "  \"routes\": [" +
                    "    {" +
                    "      \"origin\": \"" + fromCity + "\"," +
                    "      \"destination\": \"" + toCity + "\"," +
                    "      \"beginDate\": \"" + fromDate.substring(4, 6) + "/" + fromDate.substring(6, 8) + "/" + fromDate.substring(0, 4) + "\"" +
                    "    }" +
                    (   // 返程route
                            StringUtils.isNotBlank(retDate) ?
                                    ",{" +
                                            "\"origin\": \"" + toCity + "\"," +
                                            "\"destination\": \"" + fromCity + "\"," +
                                            "\"beginDate\": \"" + retDate.substring(4, 6) + "/" + retDate.substring(6, 8) + "/" + retDate.substring(0, 4) + "\"" +
                                            "}"
                                    :
                                    ""
                    ) +
                    "  ]," +
                    "  \"daysToLeft\": 0," +
                    "  \"daysToRight\": 6," +
                    "  \"version\": 2," +
                    "  \"adultCount\": " + (adult + child + infant) + "," +
                    "  \"childCount\": 0," +
                    "  \"infantCount\": {" +
                    "    \"lap\": 0," +
                    "    \"seat\": 0" +
                    "  }," +
                    "  \"promoCode\": \"\"," +
                    "  \"currency\": \"" + cur + "\"" +
                    "}";
            try {
                ip_rand = token.split("&")[1];
            }catch (Exception e){

            }
            Map<String, String> search_result = sendPostSO("availability", JSONObject.fromObject(params).toString(), token, aes_auth, "搜索航班");
            re = search_result;
            String code = search_result.get("code");
            String result = search_result.get("content");

            if("search".equals(stage)&&(result.contains("Invalid market"))){
                JRedisUtil.setKey("NODATA2" + fromCity + toCity + fromDate, "1", 24 * 3600);
                throw new DataException();
            }

            //如果包含451重新获取token、重新搜索航班
            if ((result.contains("errorcode\": \"451")||result.contains("Bad request"))&&!result.contains("Invalid market")){
                String timestamp1 = rand();
                Map<String, String> getauth = sendPostSO("v2/accessToken", "", "&"+timestamp1, "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb21wYW55IjoiQ0VCVSBBaXIgSW5jLiIsIm5hbWUiOiJvbW5pWCIsInZlcnNpb24iOjEsImNvZGVWZXJzaW9uIjoiUHRFRHNWNVA1QUhMb0FoWnk3eHE1SW5XbGZOTW9WaDkifQ.rJdOfwQPOLGObQUkZOX0eEfpXmqHtAkeXNLjorQvQj4", "accessToken");
                String tokenCode = getauth.get("code");
                logger.info(logId+"--"+"重试CEB获取token状态码:" + tokenCode);
                String tokenResult = getauth.get("content");
                logger.info(logId+"--"+"重试CEB获取token结果:" + tokenResult);
                aes_auth = JSONObject.fromObject(tokenResult).getString("Authorization");
                token = JSONObject.fromObject(tokenResult).getString("X-Auth-Token") + "&" + timestamp1;
                total_token = token + "@@@" + aes_auth;

                //重新搜索航班
                search_result = sendPostSO("availability", JSONObject.fromObject(params).toString(), token, aes_auth, "");
                code = search_result.get("code");
                result = search_result.get("content");
            }
            if (result.indexOf("Multiple services match the specified type") > 0) {
                throw new DataException();
            }

            if("search".equals(stage)&&(result.contains("Invalid market"))){
                JRedisUtil.setKey("NODATA2" + fromCity + toCity + fromDate, "1", 24 * 3600);
                throw new DataException();
            }

            if (code.equals("200")) {
                if(result.contains("currencyCode")){
                    logger.info(logId+"--"+"宿务搜索成功-"+code);
                    if(!stage.equals("book")&&!"order".equals(stage)){
                        locklist.offer(total_token);
                    }
                    clear_add_cookie(true);
                }
                JSONObject result_json = JSONObject.fromString(result);
                // 去程解文
                rtn.add(parserRouting(result_json.getJSONArray("routes"), result_json.getString("currencyCode"), false));
                // 返程解析
                if (StringUtils.isNotBlank(retDate))
                    rtn.add(parserRouting(result_json.getJSONArray("routes"), result_json.getString("currencyCode"), true));
                if ("search".equals(stage) && rtn.size() == 0) {
                    Routing nullRouting = new Routing();
                    nullRouting.getExtraInfo().put("nodata", "");
                    JRedisUtil.setKey("NODATA2" + fromCity + toCity + fromDate, "1", 3 * 24 * 3600);
                    logger.info(logId+"--"+fromCity+toCity+fromDate+"宿务航班数量为0");
                    throw new DataException();
                }
            } else {
                logger.info(logId+"--"+"宿务搜索状态码不对-"+re);
                throw new DataException();
            }
        } catch (Exception exp) {
            logger.info(logId+"--"+"-"+fromCity+toCity+fromDate+"-"+"宿务请求解析异常_"+ip_rand+"-"+re.toString().replaceAll("\n",""));
            exp.printStackTrace();
            System.out.println(" php5j ip " + ip);
            throw exp;
        }
        return rtn;
    }


    public Map<String, String> sendPostSONew(String action, String params, String token, String aes_auth, String tag) {
        Map<String, String> map = new HashMap<>();

        try {
            Map<String, String> headers = new HashMap<>();

            String uniqueid = UUID.randomUUID().toString().replaceAll("-", "").substring(0, "Kk1Vkrz3Zb7Bp7".length());
            if (action.equals("accessToken")) {
                action = (String) PHP5JTask.invoke.invokeFunction("encrypt", action, PARK);
                headers.put("uniqueid", uniqueid);
            } else {
                action = (String) PHP5JTask.invoke.invokeFunction("encrypt", action, aes_auth.toString() + token + PARK);
                action = action.substring(0, 40) + aes_auth + token + action.substring(40);
                params = (String) PHP5JTask.invoke.invokeFunction("encrypt", params, aes_auth.toString() + token + PARK);
                params = params.substring(0, 40) + aes_auth + token + params.substring(40);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("content", params);
                params = jsonObject.toString();
            }

            if (StringUtils.isNotBlank(token)) {
                headers.put("X-Auth-Token", token);
            }
            if (StringUtils.isNotBlank(aes_auth)) {
                headers.put("authorization", "Bearer " + aes_auth);
            }
            headers.put("referer", "https://www.cebupacificair.com/");
            headers.put("origin", "https://www.cebupacificair.com");
            headers.put("content", action);
            headers.put("content-type", "application/json");
            headers.put("accept-encoding", "gzip, deflate, br, zstd");
            headers.put("accept-language", "en,zh-CN;q=0.9,zh;q=0.8");

            headers.put("user-agent", USER_AGENT);
            headers.put("accept", "application/json, text/plain, */*");
//            headers.put("scope", "omnix");
            headers.put("host", "soar.cebupacificair.com");

            logger.info(logId+"--"+"开始请求SO=======");
            HttpTls.Request request = new HttpTls.Request();
            request.setAppid(HttpTls.getInstance().appid);
            request.setUrl("https://soar.cebupacificair.com/ceb-omnix_proxy");
            request.setMethod("post");
            if (StringUtils.isNotBlank(params)) {
                request.setBody(params.toString());
            } else {
                request.setBody("");
            }
            request.setHeaders(headers);
            request.setUserAgent(USER_AGENT);

            if (!stage.equals("search")) {
                Random random = new Random();
                String sessionId = Integer.toString(random.nextInt(Integer.MAX_VALUE));
                request.setProxyIp("zproxy.lum-superproxy.io");
                request.setProxyAuth("lum-customer-hl_d8729b83-zone-residential-country-ph-session-session" + System.currentTimeMillis() + ":b9r4dfau02xp");
                request.setProxyPort(22225);
            } else {
                String sid = UUID.randomUUID().toString().substring(0, 8);
                ProxyIp proxyIp = IPProxyUtil.getGWNTNTProxyIP("ph", sid, 0, wrapper);
                request.setProxyIp(proxyIp.getIp());
                request.setProxyAuth(proxyIp.getAuth());
                request.setProxyPort(proxyIp.getPort());
            }


            DefaultHttpClient client = wrapClient1(getHttpClient(""));
            HttpHost proxy = new HttpHost("zproxy.lum-superproxy.io", 22225, "http");
            client.getParams().setParameter(ConnRoutePNames.DEFAULT_PROXY, proxy);
            AuthScope auth = new AuthScope("zproxy.lum-superproxy.io", 22225);
            String proxyUser = "lum-customer-hl_d8729b83-zone-residential-country-th-session-session" + System.currentTimeMillis();

            Credentials credentials = new org.apache.http.auth.NTCredentials(proxyUser, "b9r4dfau02xp", "", "");
            client.getCredentialsProvider().setCredentials(auth, credentials);

            HttpPost post = new HttpPost("https://soar.cebupacificair.com/ceb-omnix_proxy");


//            String host = "https://www.cebupacificair.com";
//            post.setHeader("authorization", "Bearer " + "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb21wYW55IjoiQ0VCVSBBaXIgSW5jLiIsIm5hbWUiOiJvbW5pWCIsInZlcnNpb24iOjEsImNvZGVWZXJzaW9uIjoiUHRFRHNWNVA1QUhMb0FoWnk3eHE1SW5XbGZOTW9WaDkifQ.rJdOfwQPOLGObQUkZOX0eEfpXmqHtAkeXNLjorQvQj4");
//            post.setHeader("uniqueid", uniqueid);
//            post.setHeader("content-type", "application/json");
//            post.setHeader("origin", host);
//            post.setHeader("host", "soar.cebupacificair.com");
//            post.setHeader("referer", host + "/");
//            post.setHeader("content", action);
//            post.setHeader("accept", "application/json, text/plain, */*");
//            post.setHeader("accept-language", "en,zh-CN;q=0.9,zh;q=0.8");
//            post.setHeader("accept-encoding", "gzip, deflate, br, zstd");
//            post.setHeader("user-agent", USER_AGENT);


//            Iterator<String> iterator = headers.keySet().iterator();
//            while (iterator.hasNext()){
//                String key = iterator.next();
//                post.addHeader(key, headers.get(key));
//                System.out.println(key + "==" + headers.get(key));
//            }

//            for (Header allHeader : post.getAllHeaders()) {
//                System.out.println(allHeader.getName() + "==" + allHeader.getValue());
//            }


            if (StringUtils.isNotBlank(params)) {
                post.setEntity(new StringEntity(params, "utf-8"));
            }
//            if(1==1){
//                CloseableHttpResponse execute = client.execute(post);
//                System.out.println("java code:" + execute.getStatusLine());
//                String s = readHtmlContentFromEntity(execute.getEntity());
//                System.out.println(s);
//                map.put("code", execute.getStatusLine().getStatusCode() + "");
//                map.put("content", s);
//                return map;
//            }


//            XqtHttpResponse testpostkang = testpostkang("https://soar.cebupacificair.com/ceb-omnix_proxy", post.getAllHeaders(), params);
//            System.out.println(testpostkang.getCode());
//            System.out.println(testpostkang.getResult());


            request.setRedirect(true);
//            request.setRandomJa3(true);
            HttpTls.Response response = HttpTls.tlsProxy(request);
            String code = response.getCode();
            logger.info(logId+"--"+"本次SO请求CODE:" + code);

            if (code.equals("0")) {
                map.put("code", "200");
                map.put("content", response.getResult());
                return map;
            }

            map.put("code", code);
            map.put("content", response.getResult());
            return map;
        } catch (Exception e) {
            e.printStackTrace();
//            logger.error(order_no+e.getMessage(),e);
            map.put("code", "403");
            map.put("content", "ERROR_请求异常了");
            return map;
        }

    }
    private String rand(){
        StringBuilder str=new StringBuilder();//定义变长字符串
        Random random=new Random();
        //随机生成数字，并添加到字符串
        for(int i=0;i<8;i++){
            str.append(random.nextInt(10));
        }
        return str.toString();
    }

    public static String generateHMAC(String unid,String notice){
        try {
            String message = unid + XAT + notice;  // Equivalent to `fe`
            String algorithm = "HmacSHA256";
            SecretKeySpec secretKeySpec = new SecretKeySpec(SECR.getBytes(StandardCharsets.UTF_8), algorithm);
            Mac mac = Mac.getInstance(algorithm);
            mac.init(secretKeySpec);
            byte[] hmacBytes = mac.doFinal(message.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hmacBytes);
        }catch (Exception e){
            e.printStackTrace();
        }
        return "";
    }

    public static String generateHMACForSearch(String content){
        try {
            String BSKY = "kAYDgiHzp0TeNkgUjMdHFANw3pATzAjE";
            String algorithm = "HmacSHA256";
            SecretKeySpec secretKeySpec = new SecretKeySpec(BSKY.getBytes(StandardCharsets.UTF_8), algorithm);
            Mac mac = Mac.getInstance(algorithm);
            mac.init(secretKeySpec);
            byte[] hmacBytes = mac.doFinal(content.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hmacBytes);
        }catch (Exception e){
            e.printStackTrace();
        }
        return "";
    }

    public Map<String, String> sendPostDL(String action, String params, String token, String aes_auth, String tag) {

        Map<String, String> result = new HashMap<>();
        try {
            Map<String, String> headers = new HashMap<>();
            String x_path = "";
            try {
                x_path = (String) PHP5JTask.invoke.invokeFunction("encrypt", "/"+action, AESK);
            } catch (ScriptException e) {
                throw new RuntimeException(e);
            } catch (NoSuchMethodException e) {
                throw new RuntimeException(e);
            }
            if (action.equals("v2/accessToken")) {
                uniqueid = UUID.randomUUID().toString().toLowerCase();
                headers.put("uniqueid", uniqueid);
                String notice = System.currentTimeMillis()+"";
                JSONObject jsonObject_pa = new JSONObject();
                jsonObject_pa.put("message", generateHMAC(uniqueid, notice));
                jsonObject_pa.put("uniqueId", uniqueid);
                jsonObject_pa.put("nonce", notice);
                // System.out.println(jsonObject_pa.toString());
                params = (String) PHP5JTask.invoke.invokeFunction("encrypt", jsonObject_pa.toString(), PARK);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("content", params);
                params = jsonObject.toString();
            } else {
                params = (String) PHP5JTask.invoke.invokeFunction("encrypt", params, aes_auth + token + PARK);
                params = params.substring(0, 40) + aes_auth + token + params.substring(40);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("content", params);
                params = jsonObject.toString();
            }

            if (StringUtils.isNotBlank(token)) {
                headers.put("X-Auth-Token", token);
            }
            if (StringUtils.isNotBlank(aes_auth)) {
                headers.put("Authorization", "Bearer " + aes_auth);
            }
            headers.put("accept", "application/json, text/plain, */*");
            headers.put("accept-language", getaccept_lunage());
            headers.put("content-type", "application/json");
            headers.put("origin", "https://www.cebupacificair.com");
            headers.put("priority", "u=1, i");
            headers.put("referer", "https://www.cebupacificair.com");
            try {
                String chromevv = USER_AGENT.split("Chrome/")[1].split("\\.")[0];
                headers.put("sec-ch-ua", "\"Chromium\";v=\""+chromevv+"\", \"Google Chrome\";v=\""+chromevv+"\", \"Not.A/Brand\";v=\"24\"");
            }catch (Exception e){
                headers.put("sec-ch-ua", "\"Chromium\";v=\""+138+"\", \"Google Chrome\";v=\"115\", \"Not.A/Brand\";v=\"24\"");
            }
             headers.put("sec-ch-ua-mobile", "?0");
             headers.put("sec-fetch-dest", "empty");
             headers.put("sec-fetch-mode", "cors");
             headers.put("sec-fetch-site", "same-site");
            headers.put("User-Agent", USER_AGENT);
            headers.put("x-path", x_path);
            headers.put("accept-encoding", "gzip, deflate, br, zstd");
            headers.put("host", "soar.cebupacificair.com");



            NewHttpTls.Request request = new NewHttpTls.Request();
            request.setMethod("POST");
            request.setAppid("4zlaj1ku9mfl2iva2rmqno444ej8gjjx");
            request.setUrl("https://soar.cebupacificair.com/ceb-omnix-proxy-v2/" + action);
            request.setRedirect(false);
            request.setRandomJa3(false);
            if (StringUtils.isNotEmpty(params)) {
                request.setBody(params);
            }
            request.setHeaders(headers);
            if (StringUtils.isNotBlank(USER_AGENT)) {
                request.setUserAgent(USER_AGENT);
            }

            String username = http_proxys.split("@")[0].split("//")[1].split(":")[0];
            String pass = http_proxys.split("@")[0].split("//")[1].split(":")[1];
            String host = http_proxys.split("@")[1].split(":")[0];
            String port = http_proxys.split("@")[1].split(":")[1];
            request.setProxyIp(host);
            request.setProxyAuth(username + ":" + pass);
            request.setProxyPort(Integer.valueOf(port));

            String _request = SerializerUtil.serialize(request);
            DefaultHttpClient client1 = getHttpClient("");
//            HttpPost post = new HttpPost("http://************:8888/tls");
//            HttpPost post = new HttpPost("http://api-hk.zjdanli.com:8888/tls");
//            HttpPost post = new HttpPost("http://***************:8889/tls");
//            HttpPost post = new HttpPost("http://***************:8887/tls");
//            HttpPost post = new HttpPost("http://***************:8886/tls");//成功率很低
//            HttpPost post = new HttpPost("http://***************:8888/tls");//成功率很低
//            HttpPost post = new HttpPost("http://***************:8889/tls");
            HttpPost post = new HttpPost("http://akmtls.zjdanli.com/tls");
            post.setHeader("content-type", "application/json");
            post.setHeader("accept-encoding", "gzip, deflate");
            post.setEntity(new StringEntity(_request));
            String html = readHtmlContentFromEntity(client1.execute(post).getEntity());
            post.releaseConnection();
            NewHttpTls.Response response = SerializerUtil.deserializeObj(html, NewHttpTls.Response.class);
            String code = response.getCode();
            if (code.equals("0")) {
                code = "200";
            }
            logger.info("{}{}请求状态码{}", "danliTLS", tag, code);
//            logger.info("msg:" + response.getMsg());

            if (!code.equals("200" )&& !code.equals("201")) {

                logger.info("{}请求失败;报文:" + tag + response.getResult());
            }
            String content = response.getResult();
            result.put("code", code);
            result.put("content", content);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("CEB请求异常" + e.getLocalizedMessage());
            result.put("code", "300");
            result.put("content", "ERROR_搜索航班失败");
            return result;
        }

    }

    public Map<String, String> sendPostSO(String action, String params, String token, String aes_auth, String tag) {
        if(1==1){
            return sendPostDL(action, params, token, aes_auth, tag);
        }
        com.alibaba.fastjson.JSONObject headers = new com.alibaba.fastjson.JSONObject();
        token = token.split("&")[0];

        com.alibaba.fastjson.JSONObject sendInfo = new com.alibaba.fastjson.JSONObject();
        //目标url
        sendInfo.put("url", "https://soar.cebupacificair.com/ceb-omnix-proxy-v2/" + action);
        System.out.println("本次所用代理:"+http_proxys);
        sendInfo.put("proxy", http_proxys);

        String x_path = null;
        //目标网站请求头
        try {
            try {
                x_path = (String) PHP5JTask.invoke.invokeFunction("encrypt", "/"+action, AESK);
            } catch (ScriptException e) {
                throw new RuntimeException(e);
            } catch (NoSuchMethodException e) {
                throw new RuntimeException(e);
            }

            if (action.equals("v2/accessToken")) {
                uniqueid = UUID.randomUUID().toString().toLowerCase();
                action = (String) PHP5JTask.invoke.invokeFunction("encrypt", action, PARK);
                headers.put("uniqueid", uniqueid);
                String notice = System.currentTimeMillis()+"";
                JSONObject jsonObject_pa = new JSONObject();
                jsonObject_pa.put("message", generateHMAC(uniqueid, notice));
                jsonObject_pa.put("uniqueId", uniqueid);
                jsonObject_pa.put("nonce", notice);
                // System.out.println(jsonObject_pa.toString());
                params = (String) PHP5JTask.invoke.invokeFunction("encrypt", jsonObject_pa.toString(), PARK);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("content", params);
                params = jsonObject.toString();
            } else {
                action = (String) PHP5JTask.invoke.invokeFunction("encrypt", action, aes_auth + token + PARK);
                action = action.substring(0, 40) + aes_auth + token + action.substring(40);
//                System.out.println("加密前参数::"+params);
                params = (String) PHP5JTask.invoke.invokeFunction("encrypt", params, aes_auth + token + PARK);
                params = params.substring(0, 40) + aes_auth + token + params.substring(40);
//                System.out.println("加密后参数::"+params);

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("content", params);
                params = jsonObject.toString();
            }

            if (StringUtils.isNotBlank(token)) {
                headers.put("X-Auth-Token", token);
            }
            if (StringUtils.isNotBlank(aes_auth)) {
                headers.put("Authorization", "Bearer " + aes_auth);
            }
            headers.put("referer", "https://www.cebupacificair.com");
            headers.put("origin", "https://www.cebupacificair.com");
            // headers.put("content", action);
            headers.put("x-path", x_path);
            headers.put("content-type", "application/json");
//            headers.put("accept", "application/json, text/plain, */*");
            headers.put("accept-encoding", "gzip;q=1.0, compress;q=0.5");
            headers.put("accept-language", getaccept_lunage());
            headers.put("User-Agent", USER_AGENT);
            headers.put("accept", "*/*");
            headers.put("host", "soar.cebupacificair.com");
        } catch (Exception e) {
            e.printStackTrace();
        }
        sendInfo.put("headers", headers);
        //请求方式(默认get)
        sendInfo.put("method", "post");
        //选择发包器(默认curl)
//        logger.info("本次请求过来的标志："+stage+";准备用发包器CURL");

        if(stage.equals("book")||"order".equals(stage)){
            sendInfo.put("random_tls_extension_order", "true");
            sendInfo.put("debug_log", false);
            sendInfo.put("types", "curl");

        }else {
            sendInfo.put("random_tls_extension_order", "true");
            sendInfo.put("debug_log", false);
            sendInfo.put("types", "curl");

        }
        //请求参数
        //请求头 "Content-Type"是"application/x-www-form-urlencoded" 这个类型用data
        //请求头 "Content-Type"是 “application/json” 这个类型用json
//        sendInfo.put("json", json);
        if (!params.equals("")) {
            com.alibaba.fastjson.JSONObject jsonObject2 = new com.alibaba.fastjson.JSONObject();
            String content = JSONObject.fromObject(params).getString("content");
            jsonObject2.put("content", content);
            sendInfo.put("json", jsonObject2);
        } else {
            sendInfo.put("json", params);
        }
        //超时时间（默认30s）
        sendInfo.put("timeout", 60);
        //是否重定向（默认是）
        sendInfo.put("redirect", true);

        TLSForwardUtil TLSForwardUtil = new TLSForwardUtil();

        Map<String, String> map = TLSForwardUtil.sendTls(sendInfo, "");
        logger.info(logId+"--"+tag+"tls响应:"+map.get("code"));
        String code = map.get("code");
        if(code.equals("421")){
            JRedisUtil.del("PHP5J_SEARCH_TOKE");
            JRedisUtil.del("PHP5J_SEARCH_AUTH");
        }
        String content = map.get("content");
        if (content.contains("list index out of range")){
            logger.info(logId+"--"+"索引越界参数:"+sendInfo);
        }
        if (content.contains("Access Denied")){
            map.put("code", "403");
        }

//        System.out.println(tag+"请求状态码:" + code);
//        System.out.println(tag+"请求content:" + content);
        try {
            JSONObject jsonObject1 = new JSONObject(content);
//            if(StringUtils.isNotwBlank(jsonObject1.getString("response"))){
            String response = jsonObject1.getString("response");
            map.put("content", response);
//            }
        }catch (Exception e){
            logger.error("解析curl_tls_返回报文异常"+e.getMessage()+map);
            return map;
        }


        return map;
    }


    static Random random = new Random();

    public String getaccept_lunage(){
        String init_str = "zh-CN,zh;q=0.9";
        int i = random.nextInt(luanges.size());
        for (int j = 0; j < i; j++) {
            init_str = init_str + "," + luanges.get(random.nextInt(luanges.size()));
        }
        // System.out.println(init_str);
        return init_str;
    }

    private Map<String, List<Flight>> parserRouting(JSONArray routing, String currencyCode, boolean isRt) {
        Map<String, List<Flight>> map = new HashMap<>();

        JSONArray jsonArray;
        if (isRt) {
            jsonArray = routing.getJSONObject(1).getJSONArray("journeys");
        } else {
            jsonArray = routing.getJSONObject(0).getJSONArray("journeys");
        }

        if (jsonArray == null || jsonArray.length() < 1) {
            return map;
        }

        for (int i = 0; i < jsonArray.length(); i++) {

            JSONObject routerJson = jsonArray.getJSONObject(i);
            JSONArray segmentsJson = routerJson.getJSONArray("segments");
            List<Flight> list = new ArrayList<>();
            String fareClass = routerJson.getString("fareClass");
            for (int j = 0; j < segmentsJson.length(); j++) {
                JSONObject flightJson = segmentsJson.getJSONObject(j);
                Flight f = new Flight();

                String carrierCode = flightJson.getJSONObject("identifier").getString("carrierCode");
                String equipment = flightJson.getString("equipmentType");
                String departureDate = flightJson.getJSONObject("designator").getString("departure");
                departureDate = departureDate.replaceAll("\\D", "");
                departureDate = departureDate.substring(0, 12);
                String arrivalDate = flightJson.getJSONObject("designator").getString("arrival");

                arrivalDate = arrivalDate.replaceAll("\\D", "");
                arrivalDate = arrivalDate.substring(0, 12);

                String routeNumber = flightJson.getJSONObject("identifier").getString("identifier");
                routeNumber = routeNumber.replaceAll("\\ +", "");
                String departureStation = flightJson.getJSONObject("designator").getString("origin");
                String arrivalStation = flightJson.getJSONObject("designator").getString("destination");

                f.setCarrier(carrierCode);
                f.setFlightNumber(carrierCode + routeNumber);
                f.setAircraftCode(equipment);
                f.setDepTime(departureDate);
                f.setArrTime(arrivalDate);
                f.setDepAirport(departureStation);
                f.setArrAirport(arrivalStation);
                f.setCabin(fareClass);
                f.setSegmentType(1);
                list.add(f);
            }

            String price = routerJson.getString("fareTotal");

            if (StringUtils.isBlank(price)) {
                continue;
            }

            int available = routerJson.getInt("availableCount");
            String flight_key = routerJson.getString("journeyKey");
            String fare_key = routerJson.getString("fareAvailabilityKey");
            JSONArray segments = routerJson.getJSONArray("segments");
            String segmentKey = "";
            for (int j = 0; j < segments.length(); j++) {
                JSONObject segment = segments.getJSONObject(j);
                segmentKey = segmentKey + "#" + segment.getString("segmentKey");
            }
            int pp = (int) Double.parseDouble(exchange(price, currencyCode)) - 10;

            map.put(i + "_" + pp + "_" + 10 + "_" + available + "_&" + flight_key + "&" + fare_key + "&" + currencyCode + "&" + price + "&" + segmentKey, list);
        }

        return map;
    }

//    private String isCur(String fromCity){
//
//        if(StringUtils.isNotBlank(currence)){
//            return currence;
//        }
//
//        String cur = "PHP";
//        if("SYD".equals(fromCity) || "MEL".equals(fromCity)){
//            cur  = "AUD";
//        }else  if("BWN".equals(fromCity)){
//            cur  = "BND";
//        }else  if("HKG".equals(fromCity)){
//            cur  = "HKD";
//        }else  if("MFM".equals(fromCity)){
//            cur  = "MOP";
//        }else  if("SIN".equals(fromCity)){
//            cur  = "SGD";
//        }else  if("BKK".equals(fromCity)){
//            cur  = "THB";
//        }else  if("TPE".equals(fromCity)){
//            cur  = "TWD";
//        }else  if("DXB".equals(fromCity)){
//            cur  = "AED";
//        }else  if("REP".equals(fromCity) || "DPS".equals(fromCity) || "CGK".equals(fromCity)
//                || "GUM".equals(fromCity) || "HAN".equals(fromCity)
//                || "SGN".equals(fromCity)){
//            cur  = "USD";
//        }else  if("PEK".equals(fromCity) || "PVG".equals(fromCity) || "XMN".equals(fromCity)
//                || "CAN".equals(fromCity) || "SZX".equals(fromCity)){
//            cur  = "CNY";
//        }else  if("PEK".equals(fromCity) || "PVG".equals(fromCity) || "XMN".equals(fromCity) || "SZX".equals(fromCity)
//                || "CAN".equals(fromCity)){
//            cur  = "CNY";
//        }else  if("FUK".equals(fromCity) || "NGO".equals(fromCity) || "NRT".equals(fromCity) || "KIX".equals(fromCity)){
//            cur  = "JPY";
//        }else  if("BKI".equals(fromCity) || "KUL".equals(fromCity) || "SDK".equals(fromCity)){
//            cur  = "MYR";
//        }else  if("ICN".equals(fromCity) || "PUS".equals(fromCity)){
//            cur  = "KRW";
//        }
//
//        return cur;
//    }


    public void setCurrence(String currence) {
        this.currence = currence;
    }

    public String sendGetRequest(HttpClient httpClient, String url, String token) {
        String responseContent = null; // 响应内容
        HttpGet getMethod = new HttpGet(url);
        getMethod.setHeader("Host", "mobile-api.cebupacificair.com");
        getMethod.setHeader("Authorization-Secret", entryToken(token, false, url));
        getMethod.setHeader("User-Agent", USER_AGENT);
        getMethod.setHeader("Connection", "keep-alive");
        getMethod.setHeader("Accept", "application/json");
        getMethod.setHeader("Accept-Language", "zh-cn");
        if (!"".equals(token) && token != null) {
            getMethod.setHeader("Authorization", token);
        }
        getMethod.setHeader("Accept-Encoding", "gzip,deflate");
        getMethod.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);
        try {
            HttpResponse response = httpClient.execute(getMethod);
            HttpEntity entity = response.getEntity();

            entity = response.getEntity();
            responseContent = readHtmlContentFromEntity(entity);
            getMethod.releaseConnection();
            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public String sendSSLPatchRequestByJson(HttpClient httpClient, String reqURL, String params, String token) {
        String responseContent = null; // 响应内容

        try {
            HttpPatch httpPatch = new HttpPatch(reqURL); // 创建HttpPost

            httpPatch.setEntity(new StringEntity(params));
            httpPatch.setHeader("Host", "mobile-api.cebupacificair.com");
//				httpPatch.setHeader("loggly-tracking-id", LOGGLY_TRACKING_ID);
            httpPatch.setHeader("Accept", "application/json");
            if (!"".equals(token) && token != null) {
                httpPatch.setHeader("Authorization", token);
            }
            httpPatch.setHeader("Accept-Language", "zh-cn");
            httpPatch.setHeader("Accept-Encoding", "gzip,deflate");
            httpPatch.setHeader("Content-Type", "application/json; charset=utf-8");
            httpPatch.setHeader("User-Agent", USER_AGENT);
            httpPatch.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);
            httpPatch.setHeader("Connection", "keep-alive");
            httpPatch.setHeader("Authorization-Secret", entryToken(token, false, reqURL));

            // token
            HttpResponse response = httpClient.execute(httpPatch); // 执行POST请求

            System.out.println("httpclicent" + response.getStatusLine().getStatusCode());

            HttpEntity entity = response.getEntity(); // 获取响应实体
            responseContent = readHtmlContentFromEntity(entity);
            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return responseContent;
        }
    }


    public String sendSSLPatchRequestByJson(HttpClient httpClient, String reqURL, JSONObject params, String token) {
        long responseLength = 0;
        String responseContent = null; // 响应内容

        try {
            HttpPatch httpPatch = new HttpPatch(reqURL); // 创建HttpPost

            httpPatch.setEntity(new StringEntity(params.toString()));
            httpPatch.setHeader("Host", "mobile-api.cebupacificair.com");
            //				httpPatch.setHeader("loggly-tracking-id", LOGGLY_TRACKING_ID);
            httpPatch.setHeader("Accept", "application/json");
            if (!"".equals(token) && token != null) {
                httpPatch.setHeader("Authorization", token);
            }
            httpPatch.setHeader("Accept-Language", "zh-cn");
            httpPatch.setHeader("Accept-Encoding", "gzip,deflate");
            httpPatch.setHeader("Content-Type", "application/json; charset=utf-8");
            //				httpPatch.setHeader("User-Agent", USER_AGENT);
            httpPatch.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);
            httpPatch.setHeader("Connection", "keep-alive");
            httpPatch.setHeader("Authorization-Secret", entryToken(token, false, reqURL));

            // token
            HttpResponse response = httpClient.execute(httpPatch); // 执行POST请求

            System.out.println("httpclicent" + response.getStatusLine().getStatusCode());

            HttpEntity entity = response.getEntity(); // 获取响应实体
            responseContent = readHtmlContentFromEntity(entity);
            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return responseContent;
        }
    }


    public String getToken(HttpClient client, String userName) {
        try {
            String url = "https://mobile-api.cebupacificair.com/dotrez-prod-v3/api/nsk/v1/Token";
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Host", "mobile-api.cebupacificair.com");
            httpPost.setHeader("Authorization-Secret", entryToken("", false, "https://mobile-api.cebupacificair.com/dotrez-prod-v3/api/nsk/v1/Token"));
            httpPost.setHeader("Content-Type", "application/json; charset=utf-8");
            httpPost.setHeader("Connection", "keep-alive");
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("Accept-Language", "zh-cn");
            httpPost.setHeader("User-Agent", USER_AGENT);
            httpPost.setHeader("Accept-Encoding", "gzip,deflate");
            httpPost.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);

            httpPost.setEntity(new StringEntity("{\"Credentials\":{" + "\"Username\":\"" + userName + "\"," // 邮箱
                    + "\"Password\":\"Axqt599999\"," // 密码
                    + "\"Domain\":\"WWW\",\"Location\":\"MOB\"},\"ApplicationName\":\"cebumobileapp\"}"));
            HttpResponse response = client.execute(httpPost);
            HttpEntity entity = response.getEntity();
            String result = readHtmlContentFromEntity(entity);

            String token = JSONObject.fromString(result).getJSONObject("data").getString("token");
            System.out.println(token);
            httpPost.releaseConnection();
            return token;
        } catch (Exception e) {
            try {
                String url = "https://mobile-api.cebupacificair.com/dotrez-prod-v3/api/nsk/v1/Token";
                HttpPost httpPost = new HttpPost(url);
                httpPost.setHeader("Host", "mobile-api.cebupacificair.com");
                httpPost.setHeader("Authorization-Secret", AUTHORIZATION_SECRET);
                httpPost.setHeader("Content-Type", "application/json; charset=utf-8");
                httpPost.setHeader("Connection", "keep-alive");
                httpPost.setHeader("Accept", "application/json");
                httpPost.setHeader("Accept-Language", "zh-cn");
                httpPost.setHeader("User-Agent", USER_AGENT);
                httpPost.setHeader("Accept-Encoding", "gzip,deflate");
                httpPost.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);

                httpPost.setEntity(new StringEntity("{\"Credentials\":{" + "\"Username\":\"" + userName + "\"," // 邮箱
                        + "\"Password\":\"Axqt599999\"," // 密码
                        + "\"Domain\":\"WWW\",\"Location\":\"MOB\"},\"ApplicationName\":\"cebumobileapp\"}"));
                HttpResponse response = client.execute(httpPost);
                HttpEntity entity = response.getEntity();
                String result = readHtmlContentFromEntity(entity);
                System.out.println(response);
                String token = JSONObject.fromString(result).getJSONObject("data").getString("token");

                httpPost.releaseConnection();
                return token;
            } catch (Exception exp) {
                exp.printStackTrace();
            }
        }
        return null;
    }


    /**
     * 向HTTPS地址发送POST请求
     *
     * @param reqURL 请求地址
     * @param params 请求参数
     * @return 响应内容
     */
    @SuppressWarnings({"finally", "deprecation", "resource"})
    public String sendSSLPostRequestByJson(HttpClient httpClient, String reqURL, JSONObject params, String token) {
        long responseLength = 0;
        String responseContent = null; // 响应内容

        try {
            HttpPost httpPost = new HttpPost(reqURL); // 创建HttpPost

            System.out.println(params.toString().replaceAll("\"null\"", "null"));
            httpPost.setEntity(new StringEntity(params.toString().replaceAll("\"null\"", "null")));
            httpPost.setHeader("Host", "mobile-api.cebupacificair.com");
            httpPost.setHeader("Accept", "application/json");
            if (!"".equals(token) && token != null) {
                httpPost.setHeader("Authorization", token);
            }
            //				httpPost.addHeader("loggly-tracking-id", "340631");
            httpPost.setHeader("Accept-Encoding", "gzip,deflate");
            httpPost.setHeader("Content-Type", "application/json; charset=utf-8");
            httpPost.setHeader("Connection", "keep-alive");
            httpPost.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);
            httpPost.setHeader("Connection", "keep-alive");
            httpPost.setHeader("Authorization-Secret", AUTHORIZATION_SECRET);
            HttpResponse response = httpClient.execute(httpPost); // 执行POST请求

            System.out.println("httpclicent" + response.getStatusLine().getStatusCode());

            HttpEntity entity = response.getEntity(); // 获取响应实体
            responseContent = readHtmlContentFromEntity(entity);

            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return responseContent;
        }
    }

    /**
     * 向HTTPS地址发送POST请求
     *
     * @param reqURL 请求地址
     * @param params 请求参数
     * @return 响应内容
     */
    @SuppressWarnings({"finally", "deprecation", "resource"})
    public String sendSSLPostRequestByJson(HttpClient httpClient, String reqURL, String params, String token) {
        long responseLength = 0;
        String responseContent = null; // 响应内容

        try {
            System.out.println("--" + reqURL + "--");

            HttpPost httpPost = new HttpPost(reqURL); // 创建HttpPost

            httpPost.setEntity(new StringEntity(params));
            httpPost.setHeader("Host", "mobile-api.cebupacificair.com");
            httpPost.setHeader("Accept", "application/json");
            if (!"".equals(token) && token != null) {
                httpPost.setHeader("Authorization", token);
            }
            //				httpPost.addHeader("loggly-tracking-id", "340631");
            httpPost.setHeader("Accept-Language", "zh-cn");
            httpPost.setHeader("Accept-Encoding", "gzip,deflate");
            httpPost.setHeader("Content-Type", "application/json; charset=utf-8");
            httpPost.setHeader("User-Agent", USER_AGENT);
            httpPost.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);
            httpPost.setHeader("Connection", "keep-alive");

            httpPost.setHeader("Authorization-Secret", entryToken(token, false, reqURL));

            // token
            HttpResponse response = httpClient.execute(httpPost); // 执行POST请求

            System.out.println("httpclicent" + response.getStatusLine().getStatusCode());

            HttpEntity entity = response.getEntity(); // 获取响应实体
            responseContent = readHtmlContentFromEntity(entity);
            if (null != entity) {
                responseLength = entity.getContentLength();
                EntityUtils.consume(entity); // Consume response content
            }

            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return responseContent;
        }
    }

    /**
     * 向HTTPS地址发送POST请求
     *
     * @param reqURL 请求地址
     * @return 响应内容
     */
    @SuppressWarnings({"finally", "deprecation", "resource"})
    public String sendSSLPostRequestByJsonPay(HttpClient httpClient, String reqURL, String token) {
        long responseLength = 0;
        String responseContent = null; // 响应内容

        try {
            System.out.println("--" + reqURL + "--");
            HttpPost httpPost = new HttpPost(reqURL); // 创建HttpPost

            httpPost.setEntity(new StringEntity("{\"Hold\":null,\"LatestReceivedBy\":\"iOS-71624.2.26.1\",\"LatestReceivedReference\":\"\",\"RestrictionOverride\":false,\"WaiveNameChangeFee\":false,\"WaivePenaltyFee\":false,\"WaiveSpoilageFee\":false,\"DistributeToContacts\":false}"));
            httpPost.setHeader("Host", "mobile-api.cebupacificair.com");
            httpPost.setHeader("Accept", "application/json");
            httpPost.addHeader("loggly-tracking-id", "340631");
            httpPost.setHeader("Authorization", token);
            httpPost.setHeader("Accept-Language", "zh-cn");
            httpPost.setHeader("Accept-Encoding", "gzip,deflate");
            httpPost.setHeader("Content-Type", "application/json; charset=utf-8");
            httpPost.setHeader("User-Agent", USER_AGENT);
            httpPost.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);
            httpPost.setHeader("Connection", "keep-alive");
            httpPost.setHeader("Authorization-Secret", AUTHORIZATION_SECRET);

            // token
            HttpResponse response = httpClient.execute(httpPost); // 执行POST请求

            System.out.println("httpclicent" + response.getStatusLine().getStatusCode());

            HttpEntity entity = response.getEntity(); // 获取响应实体
            responseContent = readHtmlContentFromEntity(entity);
            if (null != entity) {
                responseLength = entity.getContentLength();
                EntityUtils.consume(entity); // Consume response content
            }

            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return responseContent;
        }
    }

    /**
     * 向HTTPS地址发送POST请求
     *
     * @param reqURL 请求地址
     * @param params 请求参数
     * @return 响应内容
     */
    @SuppressWarnings({"finally", "deprecation", "resource"})
    public String sendSSLPUTRequestByJson(HttpClient httpClient, String reqURL, String params, String token) {
        long responseLength = 0;
        String responseContent = null; // 响应内容

        try {
            System.out.println("--" + reqURL + "--");
            HttpPut httpPut = new HttpPut(reqURL); // 创建HttpPost

            httpPut.setEntity(new StringEntity(params.toString()));
            httpPut.setHeader("Host", "mobile-api.cebupacificair.com");
            httpPut.setHeader("Accept", "application/json");
            if (!"".equals(token) && token != null) {
                httpPut.setHeader("Authorization", token);
            }
            httpPut.setHeader("Accept-Language", "zh-cn");
            httpPut.setHeader("Accept-Encoding", "gzip,deflate");
            httpPut.setHeader("Content-Type", "application/json; charset=utf-8");
            httpPut.setHeader("User-Agent", USER_AGENT);
            httpPut.setHeader("ocp-apim-subscription-key", OCP_APIM_SUBSCRIPTION_KEY);
            httpPut.setHeader("Connection", "keep-alive");

            httpPut.setHeader("Authorization-Secret", AUTHORIZATION_SECRET);

            // token
            HttpResponse response = httpClient.execute(httpPut); // 执行POST请求

            System.out.println("httpclicent" + response.getStatusLine().getStatusCode());

            HttpEntity entity = response.getEntity(); // 获取响应实体
            responseContent = readHtmlContentFromEntity(entity);
            if (null != entity) {
                responseLength = entity.getContentLength();
                EntityUtils.consume(entity); // Consume response content
            }

            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return responseContent;
        }
    }


    private static final byte[] secret = "bmF2aXRhaXJlcHJvZmVzc2lvbmFsc2VydmljZXM=".getBytes();

    //生成一个token
    public static String creatToken(Map<String, Object> payloadMap) throws JOSEException {


        //3.先建立一个头部Header
        /**
         * JWSHeader参数：1.加密算法法则,2.类型，3.。。。。。。。
         * 一般只需要传入加密算法法则就可以。
         * 这里则采用HS256
         *
         * JWSAlgorithm类里面有所有的加密算法法则，直接调用。
         */
        JWSHeader jwsHeader = new JWSHeader(JWSAlgorithm.HS256);
        //建立一个载荷Payload
        net.minidev.json.JSONObject jsonObject = new net.minidev.json.JSONObject();
        jsonObject.putAll(payloadMap);
        Payload payload = new Payload(jsonObject);

        //将头部和载荷结合在一起
        JWSObject jwsObject = new JWSObject(jwsHeader, payload);
        //建立一个密匙
        JWSSigner jwsSigner = new MACSigner(secret);
        //签名
        jwsObject.sign(jwsSigner);
        //生成token
        return jwsObject.serialize();
    }

    public static String entryToken(String token, boolean isFirst, String url) {
        Map<String, Object> payload = new HashMap<String, Object>();
        int max = 100000, min = 999999;
        int ran2 = (int) (Math.random() * (max - min) + min);
        String log = String.valueOf(ran2);
        payload.put("appname", "cebupacific");
        if (!isFirst) {
            payload.put("NSToken", token);
        }
        //        } else {
        //        	return AUTHORIZATION_SECRET;
        //        }
        //iOS-164977.2.68.0
        payload.put("version", "2.70.0");
        payload.put("platform", "IOS");
        payload.put("url", url);
        try {
            String retInner = creatToken(payload);
            return retInner;
        } catch (JOSEException e) {
            e.printStackTrace();
        }
        return "";

    }


    public XqtHttpResponse testpostkang(String url, Header[] httpHeaders, String params) throws Exception {
        XqtHttpResponse response = new XqtHttpResponse();

        JSONObject headers = new JSONObject();
        for (Header allHeader : httpHeaders) {
            String name = allHeader.getName();
            if (name.equalsIgnoreCase("authority")) {
                continue;
            }
            String value = allHeader.getValue();
            headers.put(name, value);
        }

        String info = "{\n" +
                "\t\"appid\": \"4zlaj1ku9mfl2iva2rmqno444ej8gjjx\",\n" +
                "\t\"method\": \"post\",\n" +
                "\t\"proxyAuth\": \"lum-customer-hl_d8729b83-zone-residential-country-ph-session-session" + System.currentTimeMillis() + ":b9r4dfau02xp\",\n" +
                "\t\"proxyIp\": \"zproxy.lum-superproxy.io\",\n" +
                "\t\"proxyPort\": 22225,\n" +
                "\t\"randomJa3\": false,\n" +
                "\t\"redirect\": true,\n" +
                "\t\"timeOut\": 30\n" +
                "}";

        JSONObject jsonObject = JSONObject.fromObject(info);
        jsonObject.put("headers", headers);
        jsonObject.put("userAgent", USER_AGENT);
        jsonObject.put("url", url);
        jsonObject.put("body", params);

        HttpPost post = new HttpPost("http://***************:8888/tls");
        post.addHeader("content-type", "application/json;charset=utf-8");
        post.setEntity(new StringEntity(jsonObject.toString()));
        DefaultHttpClient httpClient = getHttpClient("");
        CloseableHttpResponse execute = httpClient.execute(post);
        String content = readHtmlContentFromEntity(execute.getEntity());

        System.out.println(content);
        JSONObject jsonObjectResult = JSONObject.fromObject(content);
        content = jsonObjectResult.getString("result");

        response.setResult(content);
        String code = jsonObjectResult.getString("code");
        response.setCode(code);

        return response;
    }

    private String isCur(String fromCity) {
        if (StringUtils.isNotBlank(currence)) {
            return currence;
        }

        String cur = "PHP";
        if ("SYD".equals(fromCity) || "MEL".equals(fromCity)) {
            cur = "AUD";
        } else if ("BWN".equals(fromCity)) {
            cur = "BND";
        } else if ("HKG".equals(fromCity)) {
            cur = "HKD";
        } else if ("MFM".equals(fromCity)) {
            cur = "MOP";
        } else if ("SIN".equals(fromCity)) {
            cur = "SGD";
        } else if ("BKK".equals(fromCity) || "DMK".equals(fromCity) || "CNX".equals(fromCity)) {
            cur = "THB";
        } else if ("TPE".equals(fromCity) || "KHH".equals(fromCity)) {
            cur = "TWD";
        } else if ("DXB".equals(fromCity)) {
            cur = "AED";
        } else if ("REP".equals(fromCity) || "DPS".equals(fromCity) || "CGK".equals(fromCity)
                || "GUM".equals(fromCity) || "HAN".equals(fromCity)
                || "SGN".equals(fromCity) || "DAD".equals(fromCity)) {
            cur = "USD";
        } else if ("PEK".equals(fromCity) || "PVG".equals(fromCity) || "XMN".equals(fromCity)
                || "CAN".equals(fromCity) || "SZX".equals(fromCity)) {
            cur = "CNY";
        } else if ("PEK".equals(fromCity) || "PVG".equals(fromCity) || "XMN".equals(fromCity) || "SZX".equals(fromCity)
                || "CAN".equals(fromCity)) {
            cur = "CNY";
        } else if ("FUK".equals(fromCity) || "NGO".equals(fromCity) || "NRT".equals(fromCity) || "KIX".equals(fromCity)
            || "CTS".equals(fromCity)) {
            cur = "JPY";
        } else if ("BKI".equals(fromCity) || "KUL".equals(fromCity) || "SDK".equals(fromCity)) {
            cur = "MYR";
        } else if ("ICN".equals(fromCity) || "PUS".equals(fromCity)) {
            cur = "KRW";
        }

        return cur;
    }


    public static void main(String[] a) throws Exception {

        // String s = generateHMAC("cc1576d9-8163-4354-92f5-5c299b961bf1", "*************");
        // System.out.println(s);
//        for (int i = 0; i < 10; i++) {
//            Thread thread = new Thread(new Runnable() {
//                public void run() {
//                    for (int j = 0; j < 1000; j++) {
//                        PHP5JTask task = new PHP5JTask();
//                        List<Routing> routings = new ArrayList<Routing>();
//                        task.site = "api.oneflya.com";
//                        task.stage = "search";
//                        task.adult = 1;
//                        task.flightData = "5J5062";
//                        task.wrapperRequest("CEB", "MNL", "********", "", routings);
//                    }
//                }
//            });
//            thread.start();
//        }
//         CacheService.getInstance().setSysconfValue("GW_PWD", "9zhu4rntOuaLD1w");
        int count = 0;
        int success = 0;
        for (int i = 0; i < 100; i++) {
            System.out.println("========================================================================");
            try {
                PHP5JTask task = new PHP5JTask();
                List<Routing> routings = new ArrayList<Routing>();
//            task.site = "api.oneflya.com";
                task.stage = "search";
                task.adult = 1;
//            task.flightData = "5J5062";
                task.wrapperRequest("MNL", "HKG", "********", "", routings);
                System.out.println("Routing：" + JSON.toJSONString(routings));
                count++;
                if(routings.size()>0){
                    success++;
                }

//                task.getBagInfo(routings.get(0), "********");
            }catch (Exception e){
                e.printStackTrace();
            }finally {
                System.out.println("成功数量:"+success+"/"+count);
            }

        }
    }

    private String getRandomCountry() {
        List<String> list = new ArrayList<>();
        list.add("us");
        list.add("ph");
        int randomIndex = new Random().nextInt(list.size());
        return list.get(randomIndex);
    }

    private void setProxy() {
        String countryCode = getRandomCountry();
        String sid = UUID.randomUUID().toString().substring(0, 8);
        if ("search".equals(stage)) {
            http_proxys =  IPProxyUtil.getGwProxyIP(countryCode, sid, 0, wrapper).getHttpProxyServer();
        } else {
            http_proxys = IPProxyUtil.getBrdProxyIP(countryCode, sid, 0, wrapper).getHttpProxyServer();
        }
    }
}

