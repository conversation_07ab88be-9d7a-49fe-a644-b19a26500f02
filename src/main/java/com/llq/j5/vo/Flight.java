package com.llq.j5.vo;


public class Flight implements java.io.Serializable{
	
	

	String carrier;
    String flightNumber;
    String depAirport;
    String depTime;
    String arrAirport;
    String arrTime;
    String stopCities = "";
    String stopAirports = null;
    String operatingCarrier  = "";
    String operatingFlightNo = "";
    String fareBasis;
    boolean codeshare = false;
    boolean codeShare = false;
    String cabin = "K";
    String cabinCode = "K";
    String aircraftCode = "";
    String cabinGrade;
    private int segmentType;
    private int num;
    String sharingFlightNumber = "";
    String originTerminal;
    String destinationTerminal;
    int cabinCount;
    String cabinClass = "1";
    String seatCount;
    Integer duration;

   
    public int getCabinCount() {
		return cabinCount;
	}

	public void setCabinCount(int cabinCount) {
		this.cabinCount = cabinCount;
	}

	public String getSharingFlightNumber() {
		return sharingFlightNumber;
	}

	public void setSharingFlightNumber(String sharingFlightNumber) {
		this.sharingFlightNumber = sharingFlightNumber;
	}

	public String getOriginTerminal() {
		return originTerminal;
	}

	public void setOriginTerminal(String originTerminal) {
		this.originTerminal = originTerminal;
	}

	public String getDestinationTerminal() {
		return destinationTerminal;
	}

	public void setDestinationTerminal(String destinationTerminal) {
		this.destinationTerminal = destinationTerminal;
	}


	public int getNum() {
		return num;
	}

	public void setNum(int num) {
		this.num = num;
	}

	public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public String getFlightNumber() {
        return flightNumber;
    }

    public void setFlightNumber(String flightNumber) {
        this.flightNumber = flightNumber;
    }

    public String getDepAirport() {
        return depAirport;
    }

    public void setDepAirport(String depAirport) {
        this.depAirport = depAirport;
    }

    public String getDepTime() {
        return depTime;
    }

    public void setDepTime(String depTime) {
        this.depTime = depTime;
    }

    public String getArrAirport() {
        return arrAirport;
    }

    public void setArrAirport(String arrAirport) {
        this.arrAirport = arrAirport;
    }

    public String getArrTime() {
        return arrTime;
    }

    public void setArrTime(String arrTime) {
        this.arrTime = arrTime;
    }

    public String getStopCities() {
        return stopCities;
    }

    public void setStopCities(String stopCities) {
        this.stopCities = stopCities;
    }

    public boolean isCodeshare() {
        return codeshare;
    }

    public void setCodeshare(boolean codeshare) {
        this.codeshare = codeshare;
    }


    public String getCabin() {
        return cabin;
    }

    public void setCabin(String cabin) {
        this.cabin = cabin;
    }

    public String getAircraftCode() {
        return aircraftCode;
    }

    public void setAircraftCode(String aircraftCode) {
        this.aircraftCode = aircraftCode;
    }  

    public boolean isCodeShare() {
		return codeshare;
	}

	public int getSegmentType() {
        return segmentType;
    }

    public void setSegmentType(int segmentType) {
        this.segmentType = segmentType;
    }

    
    public String getStopAirports() {
		return stopAirports;
	}

	public void setStopAirports(String stopAirports) {
		this.stopAirports = stopAirports;
	}

	public String getOperatingCarrier() {
		return operatingCarrier;
	}

	public void setOperatingCarrier(String operatingCarrier) {
		this.operatingCarrier = operatingCarrier;
	}

	public String getOperatingFlightNo() {
		return operatingFlightNo;
	}

	public void setOperatingFlightNo(String operatingFlightNo) {
		this.operatingFlightNo = operatingFlightNo;
	}

	public void setCodeShare(boolean codeShare) {
		this.codeShare = codeShare;
	}

	
	public String getCabinGrade() {
		return "Y";
	}

	public void setCabinGrade(String cabinGrade) {
		this.cabinGrade = cabinGrade;
	}

    public int getFlightOption() {
		return segmentType;
	}

	public String getCabinClass() {
		return cabinClass;
	}

	public void setCabinClass(String cabinClass) {
		this.cabinClass = cabinClass;
	}

	

	public String getCabinCode() {
		return cabinCode;
	}

	public void setCabinCode(String cabinCode) {
		this.cabinCode = cabinCode;
	}
	
	


	public String getFareBasis() {
		return fareBasis;
	}

	public void setFareBasis(String fareBasis) {
		this.fareBasis = fareBasis;
	}


    public String getSeatCount() {
        return seatCount;
    }

    public void setSeatCount(String seatCount) {
        this.seatCount = seatCount;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    @Override
	public String toString() {
		return "Flight [carrier=" + carrier + ", flightNumber=" + flightNumber
				+ ", depAirport=" + depAirport + ", depTime=" + depTime
				+ ", arrAirport=" + arrAirport + ", arrTime=" + arrTime
				+ ", stopCities=" + stopCities + ", stopAirports="
				+ stopAirports + ", operatingCarrier=" + operatingCarrier
				+ ", operatingFlightNo=" + operatingFlightNo + ", fareBasis="
				+ fareBasis + ", codeshare=" + codeshare + ", codeShare="
				+ codeShare + ", cabin=" + cabin + ", cabinCode=" + cabinCode
				+ ", aircraftCode=" + aircraftCode + ", cabinGrade="
				+ cabinGrade + ", segmentType=" + segmentType + ", num=" + num
				+ ", sharingFlightNumber=" + sharingFlightNumber
				+ ", originTerminal=" + originTerminal
				+ ", destinationTerminal=" + destinationTerminal
				+ ", cabinCount=" + cabinCount + ", cabinClass=" + cabinClass
                + ", duration=" + duration
                + "]";
	}


}
