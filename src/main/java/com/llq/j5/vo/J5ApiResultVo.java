package com.llq.j5.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/07/08
 **/
@Setter
@Getter
@ToString
public class J5ApiResultVo {

    /**
     * 200
     */
    private boolean success;

    /**
     * Success
     */
    private String msg;

    public List<Routing> data;

    public static J5ApiResultVo ok(List<Routing> data){
        J5ApiResultVo resultVo = new J5ApiResultVo();
        resultVo.setSuccess(true);
        resultVo.setData(data);
        return resultVo;
    }

    public static J5ApiResultVo fail(String msg){
        J5ApiResultVo resultVo = new J5ApiResultVo();
        resultVo.setSuccess(false);
        resultVo.setMsg(msg);
        return resultVo;
    }





}
