package com.llq.j5.vo;


import org.apache.logging.log4j.util.Strings;

/**
 * 运价直连乘客信息，一个订单里可能包含多名乘客，区分成人、儿童、婴儿
 *
 */

public class Passenger {
    transient final public static int TYPE_CHILD = 1;
    private long id;
    private String name; // 姓名
    private String lastName;
    private String firstName;
    private String middleName;
    private long orderId; //订单id
    private String gender; // 性别
    private int ageType = 0; // 年龄分类，-1留学生 0成人 1儿童
    private String birthday;// 出生日期
    private String nationality; // 国籍
    private String cardType; // 证件类型，如果是婴儿、儿童这个字段留空
    private String cardNum; // 证件号码，如果是婴儿、儿童这个字段填入生日
    private String cardExpired;// 证件有效期
    private String cardIssuePlace; // 证件发行地
    private String issueDate;
    private String documentCode;//Vueling - Type of accreditingDocument CRA - (Municipal Resident Certificate) CDS - (Member of Congress or Senate Credential)

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        if(Strings.isEmpty(name)){
            return this.lastName+"/"+this.firstName;
        }
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getOrderId() {
        return orderId;
    }

    public void setOrderId(long orderId) {
        this.orderId = orderId;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public int getAgeType() {
        return ageType;
    }

    public void setAgeType(int ageType) {
        this.ageType = ageType;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardNum() {
        return cardNum;
    }

    public void setCardNum(String cardNum) {
        this.cardNum = cardNum;
    }

    public String getCardExpired() {
        return cardExpired;
    }

    public void setCardExpired(String cardExpired) {
        this.cardExpired = cardExpired;
    }

    public String getCardIssuePlace() {
        return cardIssuePlace;
    }

    public void setCardIssuePlace(String cardIssuePlace) {
        this.cardIssuePlace = cardIssuePlace;
    }



    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(String issueDate) {
        this.issueDate = issueDate;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getDocumentCode() {
        return documentCode;
    }

    public void setDocumentCode(String documentCode) {
        this.documentCode = documentCode;
    }

    @Override
    public String toString() {
        return "Passenger{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", lastName='" + lastName + '\'' +
                ", firstName='" + firstName + '\'' +
                ", middleName='" + middleName + '\'' +
                ", orderId=" + orderId +
                ", gender='" + gender + '\'' +
                ", ageType=" + ageType +
                ", birthday='" + birthday + '\'' +
                ", nationality='" + nationality + '\'' +
                ", cardType='" + cardType + '\'' +
                ", cardNum='" + cardNum + '\'' +
                ", cardExpired='" + cardExpired + '\'' +
                ", cardIssuePlace='" + cardIssuePlace + '\'' +
                ", issueDate='" + issueDate + '\'' +
                ", documentCode='" + documentCode + '\'' +
                '}';
    }
}
