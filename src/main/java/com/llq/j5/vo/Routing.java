package com.llq.j5.vo;

import lombok.Data;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Data
public class Routing {

    String data; // Afare提供者的回调信息
    List<Flight> fromSegments;
    List<Flight> retSegments;

    double adultPrice;
    double adultTax;
    double childPrice;
    double childTax;
    double infantPrice;
    double infPrice;
    double infTax;

    int adultTaxType; // 0, 未含税； 1，已含税
    int childTaxType; // 0, 未含税； 1，已含税
    int infantTaxType;
    int priceType; // 0, 普通价； 1，留学生价
    int applyType; // 0, 预订价； 1，申请价
    double basePrice;

    String reservationType = "OT";
    String productType = "LCC";
    String comment = null;
    int planCategory = 2;
    int nationalityType;
    String nationality;
    String suitAge;
    int ticketTimeLimit;
    int ticketInvoiceType;
    String invoiceType = "3";

    String currency = "CNY";

//    Rule rule;

    int maxPassengerCount;
    int minPassengerCount;
    int maxFittableNum;
    String adultAgeRestriction;
    String validatingCarrier;
    String max = null;
    private int[][] combineIndexs;
    private Map<String, String> extraInfo = new HashMap<String, String>();


    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public List<Flight> getFromSegments() {
        return fromSegments;
    }

    public void setFromSegments(List<Flight> fromSegments) {
        this.fromSegments = fromSegments;
    }

    public List<Flight> getRetSegments() {
        return retSegments;
    }

    public void setRetSegments(List<Flight> retSegments) {
        this.retSegments = retSegments;
    }

    public double getAdultPrice() {
        return adultPrice;
    }

    public void setAdultPrice(double adultPrice) {
        this.adultPrice = adultPrice;
    }

    public double getAdultTax() {
        return adultTax;
    }

    public void setAdultTax(double adultTax) {
        this.adultTax = adultTax;
    }

    public double getChildPrice() {
        return childPrice;
    }

    public void setChildPrice(double childPrice) {
        this.childPrice = childPrice;
    }

    public double getChildTax() {
        return childTax;
    }

    public void setChildTax(double childTax) {
        this.childTax = childTax;
    }

    public int getAdultTaxType() {
        return adultTaxType;
    }

    public void setAdultTaxType(int adultTaxType) {
        this.adultTaxType = adultTaxType;
    }

    public int getChildTaxType() {
        return childTaxType;
    }

    public void setChildTaxType(int childTaxType) {
        this.childTaxType = childTaxType;
    }

    public int getInfantTaxType() {
        return infantTaxType;
    }

    public void setInfantTaxType(int infantTaxType) {
        this.infantTaxType = infantTaxType;
    }

    public int getPriceType() {
        return priceType;
    }

    public void setPriceType(int priceType) {
        this.priceType = priceType;
    }

    public int getApplyType() {
        return applyType;
    }

    public void setApplyType(int applyType) {
        this.applyType = applyType;
    }

    public String getMax() {
        return max;
    }

    public void setMax(String max) {
        this.max = max;
    }

    public double getBasePrice() {
        return basePrice;
    }

    public void setBasePrice(double basePrice) {
        this.basePrice = basePrice;
    }

    public String getReservationType() {
        return reservationType;
    }

    public void setReservationType(String reservationType) {
        this.reservationType = reservationType;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public int getPlanCategory() {
        return planCategory;
    }

    public void setPlanCategory(int planCategory) {
        this.planCategory = planCategory;
    }

    public int getNationalityType() {
        return nationalityType;
    }

    public void setNationalityType(int nationalityType) {
        this.nationalityType = nationalityType;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getSuitAge() {
        return suitAge;
    }

    public void setSuitAge(String suitAge) {
        this.suitAge = suitAge;
    }

    public int getTicketTimeLimit() {
        return ticketTimeLimit;
    }

    public void setTicketTimeLimit(int ticketTimeLimit) {
        this.ticketTimeLimit = ticketTimeLimit;
    }

    public int getTicketInvoiceType() {
        return ticketInvoiceType;
    }

    public void setTicketInvoiceType(int ticketInvoiceType) {
        this.ticketInvoiceType = ticketInvoiceType;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public int getMaxPassengerCount() {
        return maxPassengerCount;
    }

    public void setMaxPassengerCount(int maxPassengerCount) {
        this.maxPassengerCount = maxPassengerCount;
    }

    public int getMinPassengerCount() {
        return minPassengerCount;
    }

    public void setMinPassengerCount(int minPassengerCount) {
        this.minPassengerCount = minPassengerCount;
    }

    public int getMaxFittableNum() {
        return maxFittableNum;
    }

    public void setMaxFittableNum(int maxFittableNum) {
        this.maxFittableNum = maxFittableNum;
    }

    public String getAdultAgeRestriction() {
        return adultAgeRestriction;
    }

    public void setAdultAgeRestriction(String adultAgeRestriction) {
        this.adultAgeRestriction = adultAgeRestriction;
    }

    public String getValidatingCarrier() {
        return validatingCarrier;
    }

    public void setValidatingCarrier(String validatingCarrier) {
        this.validatingCarrier = validatingCarrier;
    }

    public int[][] getCombineIndexs() {
        return combineIndexs;
    }

    public void setCombineIndexs(int[][] combineIndexs) {
        this.combineIndexs = combineIndexs;
    }

    public Map<String, String> getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(Map<String, String> extraInfo) {
        this.extraInfo = extraInfo;
    }

//    public Rule getRule() {
//        return rule;
//    }
//
//    public void setRule(Rule rule) {
//        this.rule = rule;
//    }

    public double getInfantPrice() {
        return infantPrice;
    }

    public void setInfantPrice(double infantPrice) {
        this.infPrice = infantPrice;
        this.infantPrice = infantPrice;
    }

    public double getInfPrice() {
        if (infantPrice > 0) {
            return infantPrice;
        }
        return infPrice;
    }

    public void setInfPrice(double infPrice) {
        this.infPrice = infPrice;
    }

    public double getInfTax() {
        return infTax;
    }

    public void setInfTax(double infTax) {
        this.infTax = infTax;
    }

    @Override
    public String toString() {
        return "Routing{" +
                "data='" + data + '\'' +
                ", fromSegments=" + fromSegments +
                ", retSegments=" + retSegments +
                ", adultPrice=" + adultPrice +
                ", adultTax=" + adultTax +
                ", childPrice=" + childPrice +
                ", childTax=" + childTax +
                ", infantPrice=" + infantPrice +
                ", infPrice=" + infPrice +
                ", infTax=" + infTax +
                ", adultTaxType=" + adultTaxType +
                ", childTaxType=" + childTaxType +
                ", infantTaxType=" + infantTaxType +
                ", priceType=" + priceType +
                ", applyType=" + applyType +
                ", basePrice=" + basePrice +
                ", reservationType='" + reservationType + '\'' +
                ", productType='" + productType + '\'' +
                ", comment='" + comment + '\'' +
                ", planCategory=" + planCategory +
                ", nationalityType=" + nationalityType +
                ", nationality='" + nationality + '\'' +
                ", suitAge='" + suitAge + '\'' +
                ", ticketTimeLimit=" + ticketTimeLimit +
                ", ticketInvoiceType=" + ticketInvoiceType +
                ", invoiceType='" + invoiceType + '\'' +
                ", currency='" + currency + '\'' +
//                ", rule=" + rule +
                ", maxPassengerCount=" + maxPassengerCount +
                ", minPassengerCount=" + minPassengerCount +
                ", maxFittableNum=" + maxFittableNum +
                ", adultAgeRestriction='" + adultAgeRestriction + '\'' +
                ", validatingCarrier='" + validatingCarrier + '\'' +
                ", max='" + max + '\'' +
                ", combineIndexs=" + Arrays.toString(combineIndexs) +
                ", extraInfo=" + extraInfo +
                '}';
    }
}
