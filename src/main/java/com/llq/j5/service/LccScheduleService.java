package com.llq.j5.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.llq.j5.entity.LccSchedule;

/**
* <AUTHOR>
* @description 针对表【lcc_schedule】的数据库操作Service
* @createDate 2025-07-12 16:01:36
*/
public interface LccScheduleService extends IService<LccSchedule> {


    /**
     * 获取航线数据
     * @param airCode
     * @param desc
     * @param taskCount
     * @param memberId
     * @param logId
     * @return
     */
    LccSchedule startTask(String airCode, String desc, int taskCount, Integer memberId, Long logId);

    void endTask(LccSchedule lccSchedule);

    void stopTask(LccSchedule lccSchedule);
}
