package com.llq.j5.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.llq.j5.entity.Lcc_Huilv;
import com.llq.j5.entity.Sys_Log;
import com.llq.j5.exchange.ExchangeUtil;
import com.llq.j5.mapper.Lcc_HuilvMapper;
import com.llq.j5.service.Lcc_HuilvService;
import com.llq.j5.service.Sys_LogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
* <AUTHOR>
* @description 针对表【lcc_huiLv】的数据库操作Service实现
* @createDate 2025-07-07 16:51:55
*/
@Service
@Slf4j
public class Lcc_HuilvServiceImpl extends ServiceImpl<Lcc_HuilvMapper, Lcc_Huilv>
    implements Lcc_HuilvService {

    @Autowired
    private Sys_LogService logService;

    private final Cache<String,Lcc_Huilv> cache = CacheBuilder.newBuilder()
            //10 分钟后过期
            .expireAfterWrite(24, TimeUnit.HOURS)
            .build();


    /**
     * 获取汇率
     *
     * 从ori 转换到 dest
     *  @param ori
     * @param dest
     * @return
     */
    @Override
    public BigDecimal getExchangeRate(String ori, String dest) {
        try {
            // 先从缓存获取，如果不存在报空
            Lcc_Huilv entity = cache.get(ori, new Callable<Lcc_Huilv>() {
                @Override
                public Lcc_Huilv call() throws Exception {
                    Lcc_Huilv entity = lambdaQuery()
                            .eq(Lcc_Huilv::getBiZhongCode, ori)
                            .one();
                    /**
                     *
                     * 自动汇率获取防止有些
                     * 未配置导致异常
                     */
                    if ( null == entity ){
                        entity = saveLcchuilv(ori, dest);
                    }
                    return entity;
                }
            });
            return entity.getHuiLv();
        } catch (ExecutionException e) {
            e.printStackTrace();
            throw new RuntimeException("Lcc_HuilvService-getExchangeRate-get-error:" +ori);
        }
    }

    /**
     * 保存汇率
     * @param ori
     * @param dest
     * @return
     */
    private Lcc_Huilv saveLcchuilv(String ori, String dest) {

        //调用接口获取
        BigDecimal rate = new ExchangeUtil()
                .getExchangeRate(ori, dest);

        //记录日志
        Sys_Log sysLog=new Sys_Log();
        //以系统中LCC用户的名义发布和更新政策
        sysLog.setLogMemberId(141);
        //算是在采集政策接口模块中进行的操作
        sysLog.setLogModuleId(435);
        sysLog.setLogTime(new Date());
        //操作可能会对这些表产生影响
        sysLog.setLogTableNames("lcc_huiLv");
        logService.save(sysLog);

        //保存
        Lcc_Huilv entity = new Lcc_Huilv();
        entity.setBiZhongCode(ori);
        entity.setBiZhongName("需要人工确认");
        entity.setHuiLv(rate);
        //插入
        entity.setLogId(sysLog.getLogId());
        entity.setLogType(0);
        entity.setUpdateTime(new Date());
        save(entity);
        return entity;
    }
}




