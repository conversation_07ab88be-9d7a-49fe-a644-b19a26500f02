package com.llq.j5.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.llq.j5.dto.*;
import com.llq.j5.enums.S7ChangeRefundStatus;
import com.llq.j5.enums.TripType;
import com.llq.j5.http.J5PostUtil;
import com.llq.j5.service.Lcc_J5GetDataService;
import com.llq.j5.util.LccDataUtil;
import com.llq.j5.vo.Flight;
import com.llq.j5.vo.J5ApiResultVo;
import com.llq.j5.vo.Routing;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025/07/08
 **/
@Service
@Slf4j
public class Lcc_J5GetDataServiceImpl extends LccBaseServiceImpl implements Lcc_J5GetDataService {

    @Resource
    private J5PostUtil j5PostUtil;

    /**
     * 抓取航线数据
     *
     * @param depCity
     * @param arrCity
     * @param departureDate
     * @param currency
     * @param seq
     * @return
     */
    @Override
    public LccResultDto getOneWayJ5TicketData(String depCity, String arrCity, String departureDate,
                                              String currency, int seq) {


        /**
         * 构建查询参数
         */
        J5DataConDto conDto = J5DataConDto.buildCon(TripType.ONE_WAY.getCode(),
                depCity, arrCity,
                departureDate, currency, seq);
        String json = JSON.toJSONString(conDto);
        log.debug("getJ5TicketData-param:{}", json);
        /**
         * 执行查询
         */
        J5ApiResultVo resultVo = j5PostUtil.post(json);

        return getOneWayJ5TicketData(depCity, arrCity, departureDate, currency, resultVo);
    }

    /**
     * 抓取航线数据
     *
     * @param depCity
     * @param arrCity
     * @param departureDate
     * @param currency
     * @return
     */
    @Override
    public LccResultDto getOneWayJ5TicketData(String depCity, String arrCity, String departureDate,
                                              String currency, J5ApiResultVo apiResultVo) {
        /**
         * 解析返回
         */
        LccResultDto J5ResultDto = null;
        if (apiResultVo.isSuccess()) {
            //成功保存数据
            J5ResultDto = J5ResultDto.buildOk();
            List<LccResultDataDto> data = J5ResultDto.getData();
            //构建数据
            List<Routing> j5DataVos = apiResultVo.getData();
            buildData(j5DataVos, data);
            //出发地目的地出发日期
            data.stream().forEach(retDataDto -> {
                retDataDto.setDepCity(depCity);
                retDataDto.setArrCity(arrCity);
            });
        } else {
            //log.error("getJ5TicketData-post-fail:" + apiResultVo.getMsg());
            J5ResultDto = J5ResultDto.buildFail(apiResultVo.getMsg());
        }
        return J5ResultDto;
    }


    /**
     * 构建数据
     * @param j5DataVos
     * @param data
     */
    private void buildData(List<Routing> j5DataVos, List<LccResultDataDto> data) {
        if (null == j5DataVos || j5DataVos.isEmpty()) {
            data = null;
            return;
        }
        // 循环产品
        for (Routing routesVo: j5DataVos) {
            LccResultDataDto retDataDto = new LccResultDataDto();
            //航线
            buildDataRoute(routesVo, retDataDto);
            //获取价格
            buildDataSolution(routesVo, retDataDto);
            data.add(retDataDto);
        }
    }

    /**
     * 保存解决方案
     * @param routesVo
     * @param retDataDto
     */
    private void buildDataSolution(Routing routesVo, LccResultDataDto retDataDto) {

        /**
         * GO Basic
         */
        LccResultPriceDataDto basicPriceDto = buildPriceDataSolution(routesVo);
        retDataDto.setBasicPriceDto(basicPriceDto);
        /**
         * GO Easy
         * 获得行李和座位折扣
         */

    }

    /**
     * 构建价格
     * @param basicPriceVo
     * @return
     */
    private LccResultPriceDataDto buildPriceDataSolution(Routing basicPriceVo) {
        /**
         * 构建基本价格
         */
        LccResultPriceDataDto basicPriceDto = new LccResultPriceDataDto();
        //票面价
        BigDecimal exchangeRate = getExchangeRate(basicPriceVo.getCurrency());
        BigDecimal exchangePriceAmount = calcExchangeAmount(exchangeRate, new BigDecimal(basicPriceVo.getAdultPrice()));
        basicPriceDto.setOriginalCurrency(basicPriceVo.getCurrency());
        basicPriceDto.setExchangeRate(exchangeRate);
        basicPriceDto.setBasePrice(exchangePriceAmount);
        //税
        BigDecimal exchangeTaxAmount = calcExchangeAmount(exchangeRate, new BigDecimal(basicPriceVo.getAdultTax()));
        basicPriceDto.setTaxFeeAount(exchangeTaxAmount);

        /**
         *
         * 成人乘坐规则
         */

        /**
         * 构建航段信息
         */
        List<Flight> fromSegments = basicPriceVo.getFromSegments();
        List<LccResultPriceDataSegmentDto> priceDataSegmentDtoList = Lists.newArrayList();
        for (Flight segmentDto: fromSegments ) {
            LccResultPriceDataSegmentDto lccSegmentDto = new LccResultPriceDataSegmentDto();
            buildPriceDataSegment(segmentDto,lccSegmentDto);
            buildPriceDataSegmentDetailDto(segmentDto,lccSegmentDto,basicPriceDto);
            priceDataSegmentDtoList.add(lccSegmentDto);
        }
        basicPriceDto.setPriceDataSegmentDtoList_go(priceDataSegmentDtoList);

        /**
         * 剩余票数
         */
        int seatCount = Integer.valueOf(basicPriceVo.getExtraInfo().get("seat"));
        basicPriceDto.setSeatCount(seatCount);

        /**
         *
         *
         * 构建退改信息
         */
        /**
         * 行李规则
         */
        LccResultPriceDataSegmentDto baggageSegmentDto = priceDataSegmentDtoList
                .stream()
                .min(Comparator.comparing(LccResultPriceDataSegmentDto::getBaggageQuantity))
                .get();
        basicPriceDto.setBaggageQuantity(baggageSegmentDto.getBaggageQuantity());

        LccResultPriceDataSegmentDto baggageWeightSegmentDto = priceDataSegmentDtoList
                .stream()
                .min(Comparator.comparing(LccResultPriceDataSegmentDto::getBaggageWeight))
                .get();
        basicPriceDto.setBaggageWeight(baggageWeightSegmentDto.getBaggageWeight());

        /**
         * 退改规则
         */
        //退票
        LccResultPriceDataSegmentDto refundSegmentDto = priceDataSegmentDtoList
                .stream()
                .max(Comparator.comparing(LccResultPriceDataSegmentDto::getNoRefund))
                .get();
        basicPriceDto.setNoRefund(refundSegmentDto.getNoRefund());
        if (S7ChangeRefundStatus.ALLOW.getCode().equals(basicPriceDto.getNoRefund())) {
            //汇总退票费
            List<BigDecimal> feeList = priceDataSegmentDtoList.stream()
                    .map(LccResultPriceDataSegmentDto::getRefundFee)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            Double total = feeList.stream().mapToDouble(BigDecimal::doubleValue).sum();
            basicPriceDto.setRefundFee(transAmount(new BigDecimal(total)));
        }

        //改签
        LccResultPriceDataSegmentDto changeSegmentDto = priceDataSegmentDtoList
                .stream()
                .max(Comparator.comparing(LccResultPriceDataSegmentDto::getNoChange))
                .get();
        basicPriceDto.setNoChange(changeSegmentDto.getNoChange());
        if (S7ChangeRefundStatus.ALLOW.getCode().equals(basicPriceDto.getNoChange())) {
            //汇总退票费
            List<BigDecimal> feeList = priceDataSegmentDtoList.stream()
                    .map(LccResultPriceDataSegmentDto::getChangeFee)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            Double total = feeList.stream().mapToDouble(BigDecimal::doubleValue).sum();
            basicPriceDto.setChangeFee(transAmount(new BigDecimal(total)));
        }

        return basicPriceDto;
    }


    /**
     * 构建航段
     * @param segmentDto
     * @param lccSegmentDto
     */
    private void buildPriceDataSegment(Flight segmentDto, LccResultPriceDataSegmentDto lccSegmentDto) {
        lccSegmentDto.setDepAirport(segmentDto.getDepAirport());
        lccSegmentDto.setArrAirport(segmentDto.getArrAirport());
        lccSegmentDto.setDepTime(segmentDto.getDepTime());
        lccSegmentDto.setArrTime(segmentDto.getArrTime());
        //航班
        String trimFlightNumber = LccDataUtil.trimLeftZero(segmentDto.getFlightNumber());
        lccSegmentDto.setFlightNumber(trimFlightNumber);

    }

    /**
     * 航段明细构建
     * @param segmentDto
     * @param lccSegmentDto
     * @param basicPriceDto
     */
    private void buildPriceDataSegmentDetailDto(Flight segmentDto, LccResultPriceDataSegmentDto lccSegmentDto,
                                                LccResultPriceDataDto basicPriceDto) {

        /**
         * 子舱位
         */
        lccSegmentDto.setFareBasis(segmentDto.getFareBasis());
        lccSegmentDto.setSeatClass(segmentDto.getCabin());
        //lccSegmentDto.setSeatCount(segmentDto.getSeatCount());


        /**
         * 行李规则
         */
        lccSegmentDto.setBaggageQuantity(0);
        lccSegmentDto.setBaggageWeight(-1);


        /**
         *
         * 临时不退不改
         *
         * 接口还未取到数据
         */
        lccSegmentDto.setNoChange(1);
        lccSegmentDto.setNoRefund(1);

    }

    /**
     * 保存航线
     *
     * @param routesVo
     * @param retDataDto 是否是回程
     */
    private void buildDataRoute(Routing routesVo, LccResultDataDto retDataDto) {
        /**
         * 航程
         */
       Flight flight = routesVo.getFromSegments()
                .stream()
                .findFirst()
                .get();
        retDataDto.setDateStr_go(flight.getDepTime());
    }


}
