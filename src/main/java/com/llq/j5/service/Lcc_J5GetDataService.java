package com.llq.j5.service;


import com.llq.j5.dto.LccResultDto;
import com.llq.j5.vo.J5ApiResultVo;

/**
 * <AUTHOR>
 * @version 2025/07/08
 **/
public interface Lcc_J5GetDataService {



    /**
     * 获取接口数据
     * @param depCity
     * @param arrCity
     * @param departureDate
     * @param currency
     * @param seq
     * @return
     */
    LccResultDto getOneWayJ5TicketData(String depCity, String arrCity, String departureDate, String currency, int seq);

    LccResultDto getOneWayJ5TicketData(String depCity, String arrCity, String departureDate, String currency, J5ApiResultVo resultVo);
}
