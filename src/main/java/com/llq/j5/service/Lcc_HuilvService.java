package com.llq.j5.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.llq.j5.entity.Lcc_Huilv;

import java.math.BigDecimal;

/**
* <AUTHOR>
* @description 针对表【lcc_huiLv】的数据库操作Service
* @createDate 2025-07-07 16:51:55
*/
public interface Lcc_HuilvService extends IService<Lcc_Huilv> {


    /**
     * 默认币种
     */
    String CNY = "CNY";


    /**
     * 获取汇率
     *
     * 从ori 转换到 dest
     *  @param ori
     * @param dest
     * @return
     */
    BigDecimal getExchangeRate(String ori, String dest);
}
