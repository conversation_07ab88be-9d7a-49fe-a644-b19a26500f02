package com.llq.j5.config;

import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * j5接口地址
 *
 * @author: kongwy
 * @date: 2025年07月12日 23:48
 */
//@Configuration
@Slf4j
public class J5ApiConfig {


    public static String url;

    public static String token;

    public static int connectTimeout;

    public static int readTimeout;

    //链接池构建
    private static List<String> urls = null;
    private static int poolSize;


    @Value("${lcc.j5.url}")
    public void setUrl(String url) {
        J5ApiConfig.url = url;
        J5ApiConfig.urls = Splitter.on(";").omitEmptyStrings()
                .trimResults()
                .splitToList(url);
        if ( null == J5ApiConfig.urls || J5ApiConfig.urls.isEmpty()){
            log.error("J5ApiConfig-setUrl-{}", J5ApiConfig.urls);
            throw new RuntimeException("未配置接口请求地址");
        }
        poolSize = J5ApiConfig.urls.size();
    }

    @Value("${lcc.j5.token}")
    public void setToken(String token) {
        J5ApiConfig.token = token;
    }

    @Value("${lcc.j5.connectTimeout}")
    public  void setConnectTimeout(int connectTimeout) {
        J5ApiConfig.connectTimeout = connectTimeout;
    }

    @Value("${lcc.j5.readTimeout}")
    public  void setReadTimeout(int readTimeout) {
        J5ApiConfig.readTimeout = readTimeout;
    }

    @PostConstruct
    public void loadConfig(){
        log.info("load j5 gather config finish！！");
    }


    /**
     * 根据序号获取链接池地址
     *
     * 序号%链接池数量
     *  4%3 = 1
     *  0%3 = 0
     *  3%3 = 0
     * 目前0-2
     *
     * @param seq
     * @return
     */
    public static String getPoolUrl(int seq) {
        int idx = getPoolIdx(seq);
        return J5ApiConfig.urls.get(idx);
    }

    /**
     * 获取池索引号
     * @param seq
     * @return
     */
    public static int getPoolIdx(int seq) {
        int idx = seq % poolSize;
        return idx;
    }
}
