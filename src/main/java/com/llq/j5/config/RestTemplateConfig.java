package com.llq.j5.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 2025/07/16
 **/
@Configuration
@Slf4j
public class RestTemplateConfig {


    @Value("${lcc.j5.connectTimeout}")
    private int connectTimeout;
    @Value("${lcc.j5.readTimeout}")
    private int readTimeout;

    /**
     * RestTemplate 统一设置
     *
     * @return
     */
    @Bean
    public RestTemplate restTemplate() {
        // 1. 配置连接池（最大连接数200，单路由并发20）
        PoolingHttpClientConnectionManager pool = new PoolingHttpClientConnectionManager();
        pool.setMaxTotal(200);
        pool.setDefaultMaxPerRoute(20);
        // 2. 配置超时和重试策略
        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(pool)
                .evictIdleConnections(60, TimeUnit.SECONDS)
                .setDefaultRequestConfig(RequestConfig.custom()
                        .setConnectTimeout(connectTimeout)
                        .setSocketTimeout(readTimeout)
                        .build())
                .build();

        // 3. 使用HttpClient作为底层实现
        return new RestTemplate(new HttpComponentsClientHttpRequestFactory(httpClient));
    }

}
