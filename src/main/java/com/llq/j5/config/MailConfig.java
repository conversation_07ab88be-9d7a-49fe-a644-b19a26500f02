package com.llq.j5.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 2025/07/15
 **/
//@Configuration
@Slf4j
public class MailConfig {


    public static String userName;

    public static String authCode;

    public static String path;

    @Value("${lcc.mail.userName}")
    public  void setUserName(String userName) {
        MailConfig.userName = userName;
    }

    @Value("${lcc.mail.authCode}")
    public  void setAuthCode(String authCode) {
        MailConfig.authCode = authCode;
    }

    @Value("${lcc.mail.path}")
    public  void setPath(String path) {
        MailConfig.path = path;
    }
}
