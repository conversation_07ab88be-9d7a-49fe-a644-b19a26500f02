package com.llq.j5.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MybatisplusConfig {
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor(){
        MybatisPlusInterceptor interceptor=new MybatisPlusInterceptor();

        PaginationInnerInterceptor pageInterceptor= new PaginationInnerInterceptor(DbType.SQL_SERVER);
        pageInterceptor.setMaxLimit(100000L);

        interceptor.addInnerInterceptor(pageInterceptor);

        return interceptor;
    }
}
