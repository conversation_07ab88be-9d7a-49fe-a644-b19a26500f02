package com.llq.j5.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2025/07/11
 **/
@Data
public class LccResultPriceDataSegmentDto {


    /**
     * 记录S7数据源
     * ID
     */
    private String id;


    /**
     * 航班号，如：CA123。航班号数字前若有多余的数字0，建议去掉；如CZ006则需返回CZ6。
     */
    private String flightNumber;

    /**
     * 出发机场；IATA 三字码
     */
    private String depAirport;

    /**
     * 到达机场 IATA 三字码
     */
    private String arrAirport;

    /**
     * 起飞日期时间（当地时间），格式支持：YYYYMMDDHHMM或HHMM+1两种形式，如23:50写为2350
     */
    private String depTime;

    /**
     * 到达日期时间（当地时间），格式支持：YYYYMMDDHHMM或HHMM+1两种形式,如第二天03:40写为0340+1
     */
    private String arrTime;

    /**
     * 子舱位，如果采集不到就填"G"就行
     */
    private String seatClass;

    /**
     * 票价等级 1）fareBasis数量必须和航段数一致 2）同一旅行方向的farebasis可以不一致（多段） 3）不同旅行方向farebasis可以不一致 4）每航段1个，使用“ ; ”分割。一张票包含多个航段，一张票有多个fareBasis
     */
    private String fareBasis;


    /**
     * 库存座位数，实时库存
     */
    private int seatCount;


    /**
     * 行李额件数，单位PC，枚举值如下 0：表示无免费托运行李， -1：表示行李采用计重制， n（n>0）：表示行李采用计件制，每人可携带n件行李
     */
    private Integer baggageQuantity;

    /**
     * 行李额重量，单位KG，跟xingLiPiece配合使用，无免费行李时，填-1
     */
    private Integer baggageWeight;


    /**
     * 客票全部未使用时不可改期，1不可改期，0可以改期，如果不可改期，没必要传改期费了
     */
    private Integer noChange = 1;

    /**
     * 客票全部未使用时起飞前几小时可改期，默认0
     */
    private Integer changeHour = 0;

    /**
     * 客票全部未使用时的改期费
     */
    private BigDecimal changeFee;

    /**
     * 客票全部未使用时不可推票，1不可退票，0可以退票，如果不可退票，没必要传退票费了
     */
    private Integer noRefund = 1;

    /**
     * 客票全部未使用时起飞前几小时可退票，默认0
     */
    private Integer refundHour = 0;

    /**
     * 客票全部未使用时的退票费
     */
    private BigDecimal refundFee;


    /**
     * 客票返程未使用时不可改期，1不可改期，0可以改期，如果不可改期，没必要传改期费了
     * 往返政策才用到
     */
    private Integer noChange2 = 1;

    /**
     * 客票返程未使用时起飞前几小时可改期，默认0
     * 往返政策才用到
     */
    private Integer changeHour2 = 0;

    /**
     * 客票返程未使用时的改期费
     * 往返政策才用到
     */
    private BigDecimal changeFee2;

    /**
     * 客票返程未使用时不可推票，1不可退票，0可以退票，如果不可退票，没必要传退票费了
     * 往返政策才用到
     */
    private Integer noRefund2 = 1;

    /**
     * 客票返程未使用时起飞前几小时可退票，默认0
     * 往返政策才用到
     */
    private Integer refundHour2 = 0;

    /**
     * 客票返程未使用时的退票费
     * 往返政策才用到
     */
    private BigDecimal refundFee2;


}
