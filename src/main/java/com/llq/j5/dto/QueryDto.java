package com.llq.j5.dto;

import com.google.common.base.Strings;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2025/07/22
 **/
@Data
public class QueryDto {

    private  String depCity;
    private  String arrCity;
    private  String departureDate;
    private  String currency;


    /**
     * 参数是否不为空
     * @return
     */
    public boolean valid(){
        return !Strings.isNullOrEmpty(depCity)
                && !Strings.isNullOrEmpty(arrCity)
                && !Strings.isNullOrEmpty(departureDate)
                && !Strings.isNullOrEmpty(currency);
    }


}
