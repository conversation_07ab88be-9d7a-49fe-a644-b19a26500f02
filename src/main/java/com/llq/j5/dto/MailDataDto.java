package com.llq.j5.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/07/12
 **/
@Data
public class MailDataDto {

    /**
     * 发件人
     */
    private String from;

    /**
     * 标题
     */
    private String subject;

    /**
     * 邮件内容
     */
    private String content;


    /**
     * 附件
     */
    private List<MailFileDataDto>  attachments = new ArrayList<>();

    /**
     * 接收日期
     */
    private Date receiveDate;


    /**
     * 发件日期
     */
    private Date sendDate;

    /**
     * 消息ID
     */
    private String msgId;


}
