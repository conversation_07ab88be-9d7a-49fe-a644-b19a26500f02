package com.llq.j5.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/07/12
 **/

@Data
public class MailDto {

    private boolean success;

    private String msg;

    private List<MailDataDto> data;

    /**
     * 成功
     * @param data
     * @return
     */
    public  static MailDto ok(List<MailDataDto> data){
        MailDto mailDto = new MailDto();
        mailDto.setSuccess(true);
        mailDto.setMsg("success");
        mailDto.setData(data);
        return mailDto;
    }


    /**
     * 失败
     * @param msg
     * @return
     */
    public static MailDto fail(String msg){
        MailDto mailDto = new MailDto();
        mailDto.setSuccess(false);
        mailDto.setMsg(msg);
        return mailDto;
    }


}
