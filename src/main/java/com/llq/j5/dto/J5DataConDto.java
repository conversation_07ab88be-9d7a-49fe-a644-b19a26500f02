package com.llq.j5.dto;


import lombok.Data;

/**
 * <AUTHOR>
 * @version 2025/07/08
 **/
@Data
public class J5DataConDto {


    private String tripType;
    private String depCity;
    private String arrCity;
    private String departureDate;
    private String currency;
    private int seq;

    /**
     * 默认乘客信息
     */
    private J5DataParamPassengerDto passengerDto  = new J5DataParamPassengerDto();


    /**
     * 构建查询条件
     * @return
     * @param tripType
     * @param depCity
     * @param arrCity
     * @param departureDate
     * @param currency
     * @param seq
     */
    public static J5DataConDto buildCon(String tripType, String depCity, String arrCity, String departureDate,
                                        String currency, int seq){

        return new J5DataConDto( tripType,  depCity,  arrCity,  departureDate,  currency,  seq);
    }

    public J5DataConDto() {
    }

    public J5DataConDto(String tripType, String depCity, String arrCity, String departureDate, String currency, int seq) {
        this.tripType = tripType;
        this.depCity = depCity;
        this.arrCity = arrCity;
        this.departureDate = departureDate;
        this.currency = currency;
        this.seq = seq;

        //默认
    }


}
