package com.llq.j5.dto;

/**
 * <AUTHOR>
 * @version 2025/07/07
 **/
public class J5DataParamPassengerDto {

    /**
     * 成人
     *
     * 12岁以上
     */
    private Integer adults = 1;

    /**
     *
     * 儿童
     *
     * 12岁-18岁
     */
    private Integer children = 0;

    /**
     * 婴儿
     *
     * 2岁以下
     */
    private Integer infants = 0;

    public Integer getAdults() {
        return adults;
    }

    public void setAdults(Integer adults) {
        this.adults = adults;
    }

    public Integer getChildren() {
        return children;
    }

    public void setChildren(Integer children) {
        this.children = children;
    }

    public Integer getInfants() {
        return infants;
    }

    public void setInfants(Integer infants) {
        this.infants = infants;
    }

    @Override
    public String toString() {
        return "passengersAmount{" +
                "adults=" + adults +
                ", children=" + children +
                ", infants=" + infants +
                '}';
    }
}
