package com.llq.j5.dto;


import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/07/10
 **/

public class LccResultDto {


    private boolean success;

    private List<LccResultDataDto> data;

    private String msg;



    public LccResultDto(boolean success, List<LccResultDataDto> data, String msg) {
        this.success = success;
        this.data = data;
        this.msg = msg;
    }

    public static LccResultDto buildOk(){
        return new LccResultDto(true,new ArrayList<>(),"success");
    }

    public static LccResultDto buildFail(String msg){
        return new LccResultDto(false,null,msg);
    }


    public List<LccResultDataDto> getData() {
        return data;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getMsg() {
        return msg;
    }

    @Override
    public String toString() {
        return "LccResultDto{" +
                "success=" + success +
                ", data=" + data +
                ", msg='" + msg + '\'' +
                '}';
    }
}
