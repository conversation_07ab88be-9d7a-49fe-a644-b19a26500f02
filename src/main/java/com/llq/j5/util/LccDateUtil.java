package com.llq.j5.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2025/07/10
 **/
public class LccDateUtil {



    public static final String DATE_PATTERN_YMDHM = "yyyyMMddHHmm";


    public static final String DATE_PATTERN_YMD = "yyyyMMdd";

    public static final String DATE_PATTERN_Y_M_D = "yyyy-MM-dd";

    public static final String DATE_PATTERN_Y_M_D_UNDER = "yyyy_MM_dd";

    /**
     * 日期格式转换
     * @param inData
     * @param pattern
     * @param retPattern
     * @return
     */
    public static  String tranData(String inData,String pattern, String retPattern){
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
           Date date  = sdf.parse(inData);
           return new SimpleDateFormat(retPattern).format(date);
        } catch (Exception e) {
            e.printStackTrace();
            return inData;
        }
    }


    /**
     * 日期格式转换
     *
     * 输入 "2025-08-29T06:05:00"
     * 输出“YYYY-MM-DD”
     * @param inData
     * @return
     */
    public static String tranDate(String inData){
        try {
            return inData.substring(0,10);
        } catch (Exception e) {
            e.printStackTrace();
            return inData;
        }
    }


    /**
     * 日期格式转换
     *
     * 输入"2025-08-22T06:05:00",
     * 输出“YYYYMMDDHHMM”
     * @param inData
     * @return
     */
    public static String tranTime(String inData){
        try {
            String str = inData.replaceAll("-","")
                    .replace("T","")
                    .replaceAll(":","");
            return str.substring(0,12);
        } catch (Exception e) {
            e.printStackTrace();
            return inData;
        }
    }


    /**
     * 解析日期
     * @param date
     * @return
     */
    public static Date parseDate(String date){
        try {
            return  new SimpleDateFormat(DATE_PATTERN_Y_M_D).parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
            throw new RuntimeException("日期格式解析异常：" + e.getMessage());
        }
    }


    public static void main(String[] args) {
        //202508220605
        System.out.println(tranTime("2025-08-22T06:05:00"));
        System.out.println(tranDate("2025-08-22T06:05:00"));
    }


}
