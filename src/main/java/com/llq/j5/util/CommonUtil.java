package com.llq.j5.util;

import com.llq.j5.vo.Flight;
import com.llq.j5.vo.Routing;

import java.util.List;

public class CommonUtil {

    public static String flightToKey(List<Flight> go) {
        StringBuilder sb = new StringBuilder();

        if(go != null){
            for (Flight f : go) {
                sb.append(f.getFlightNumber()).append("/");
            }
        }
        if(sb.length() > 0){
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }


    public static String flightToKey(Routing routing) {
        StringBuilder sb = new StringBuilder();
        List<Flight> go = routing.getFromSegments();
        List<Flight> back = routing.getRetSegments();
        if(go != null){
            for (Flight f : go) {
                sb.append(f.getFlightNumber()).append("/");
            }
        }
        if(sb.length() > 0){
            sb.deleteCharAt(sb.length() - 1);
        }
        if (back != null && back.size() > 0) {
            sb.append("~");
            for (Flight f : back) {
                sb.append(f.getFlightNumber()).append("/");
            }
            sb.deleteCharAt(sb.length() - 1);
        }

        return sb.toString();
    }




}
