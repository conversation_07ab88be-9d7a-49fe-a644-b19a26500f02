package com.llq.j5.pool;

import com.alibaba.fastjson.JSON;
import com.llq.j5.config.RabbitConfig;
import com.llq.j5.entity.Lcc_ZhengCe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2025/07/17
 **/
@Component
@Slf4j
public class Timer_Pool_Lcc_ZhengCe {

    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private RabbitConfig rabbitConfig;

    /**
     * 消息确认
     *
     */
    final RabbitTemplate.ConfirmCallback confirmCallback = (correlationData, ack, cause) -> {
        if (!ack){
          log.error("s7 send - mq - fail:{}",cause);
        }
    };


    /**
     * 保存政策
     *
     * @param zhengCe
     */
    public void store(Lcc_ZhengCe zhengCe) {
        log.debug(">>>>>>>>>>{}", zhengCe);
        String json = JSON.toJSONString(zhengCe);
        //rabbitTemplate.setConfirmCallback(confirmCallback);
        rabbitTemplate.convertAndSend(
                rabbitConfig.getExchange(),
                rabbitConfig.getQueue(),
                json
        );
    }
}
