package com.llq.j5.enums;

/**
 * <AUTHOR>
 * @version 2025/07/08
 **/
public enum TripType {

    ONE_WAY("ONE_WAY","单程"),
    ROUND_TRIP("ROUND_TRIP","往返");

    private String code;
    private String desc;


    TripType(String code, String desc){
        this.code = code;
        this.desc = desc;
    }


    public String getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }


    public static String getDescByCode(String code){
        for (TripType typ: TripType.values()) {
            if (typ.getCode().equals(code)){
                return typ.getDesc();
            }
        }
        return null;
    }

}
