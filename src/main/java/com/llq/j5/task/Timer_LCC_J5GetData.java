package com.llq.j5.task;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.llq.j5.dto.LccResultDataDto;
import com.llq.j5.dto.LccResultDto;
import com.llq.j5.entity.LccSchedule;
import com.llq.j5.entity.Lcc_Airline;
import com.llq.j5.enums.AirLineType;
import com.llq.j5.enums.TripType;
import com.llq.j5.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version 2025/07/07
 **/
@Component
@Slf4j
public class Timer_LCC_J5GetData {

    @Resource
    private Lcc_AirlineService lccAirlineService;
    @Resource
    private Lcc_J5GetDataService lccJ5GetDataService;
    @Resource
    private Lcc_J5SaveDataService lccJ5SaveDataService;

    //航线查询
    public static final AirLineType AIRLINE = AirLineType.J5;
    //获取天数
    @Value("${lcc.j5.days}")
    public int DAYS;

    //指定哪个30天
    @Value("${lcc.j5.num}")
    public int num;

    //行程类型
    //public static final String[] TRIP_TYPE = {"ONE_WAY" , "ROUND_TRIP"};
    //货币
    public static final String CURRENCY = "PHP";

    @Resource
    public Executor j5TaskExecutor;

    @Resource
    private Sys_LogService logService;

    @Resource
    private LccScheduleService scheduleService;

    /**
     * j5运行
     */
    @Value("${lcc.j5.switch:false}")
    private boolean run;

    /**
     * 获取单程航线数据
     * 10分钟*60秒*1000毫秒
     * 定期获取
     */
    //@Scheduled(initialDelay = 200, fixedDelay = 10 * 60 * 1000)
    @Scheduled(initialDelay = 200, fixedDelay = 1 * 60 * 1000)
    public void queryOneWayJ5TicketData() {
        if (!run) {
            return;
        }
        log.debug("Timer_LCC_J5GetData-queryOneWayJ5TicketData-start");
        getTripTypeJ5TicketData(TripType.ONE_WAY.getCode());
        log.debug("Timer_LCC_J5GetData-queryOneWayJ5TicketData-end");

    }

    /**
     * 获取往返航线数据
     * 10分钟*60秒*1000毫秒
     * 定期获取
     */
    //@Scheduled(initialDelay = 200, fixedRate = 10 * 60 * 1000  )
    public void queryRoundTripJ5TicketData() {
        if (!run) {
            return;
        }
        log.debug("Timer_LCC_J5GetData-queryRoundTripJ5TicketData-start");
        //getTripTypeJ5TicketData(TripType.ROUND_TRIP.getCode());
        log.debug("Timer_LCC_J5GetData-queryRoundTripJ5TicketData-end");
    }

    /**
     * 根据行程类型获取价格数据
     *
     * @param tripType
     */
    private void getTripTypeJ5TicketData(String tripType) {

        /**
         *
         * step1：
         * 获取航线
         */
        List<Lcc_Airline> lccAirlineList = getAirlineList();

        Date date = DateUtil.date();
        log.info("TripType:{}-Airline: {}, Total: {}, DATE:{}",
                tripType, AIRLINE.getCode(), lccAirlineList.size(), DateUtil.formatDate(date));

        /**
         * 统计本次调用成功率
         */
        AtomicInteger successInteger = new AtomicInteger(0);
        int taskCount = lccAirlineList.size() * DAYS;
        CountDownLatch latch = new CountDownLatch(taskCount);

        //记录日志,保存政策时一批政策用一个日志，不要一个政策生成一个日志
        /*Sys_Log sysLog = new Sys_Log();
        //以系统中LCC用户的名义发布和更新政策
        sysLog.setLogMemberId(141);
        //算是在采集政策接口模块中进行的操作
        sysLog.setLogModuleId(435);
        sysLog.setLogTime(new Date());
        logService.save(sysLog);*/
        long logId = 0;

        //记录任务
        String desc = "获取航线数据[标识:" + num +"|" + TripType.getDescByCode(tripType) + ":" + DAYS + "天]";
        LccSchedule lccSchedule = scheduleService.startTask(AIRLINE.getCode(),desc,
                taskCount,141, logId);

        long start = System.currentTimeMillis();

        /**
         * step2：
         * 循环调用
         */
        AtomicInteger seqInteger = new AtomicInteger(0);
        for (int i = 0; i < DAYS; i++) {
            String departureDate = DateUtil.formatDate(DateUtil.offsetDay(date, num * 30 + i));
            for (Lcc_Airline airline : lccAirlineList) {
                j5TaskExecutor.execute(() -> {
                    int seq = seqInteger.incrementAndGet();
                    log.info("j5task-thread-start-seq:{}", seq);
                    try {
                        long begin = System.currentTimeMillis();
                       // log.info("j5task-thread-start-:{}", Thread.currentThread().getName());
                       boolean success = runDataTask(tripType,
                                logId,
                                departureDate, airline,
                                seq);
                       if (success) {
                           //记录本次调用成功率
                           int no = successInteger.incrementAndGet();
                           long end = System.currentTimeMillis() - begin;
                           log.info("j5task-thread-success-seq:{},cost:{}s", no, end/1000);
                       }
                    }finally {
                        // 确保计数器递减
                        latch.countDown();
                    }
                });
            }
        }

        /**
         * step3：
         * 计算成功率
         */
        try {
            // 阻塞等待所有任务完成
            latch.await();
            double rate = successInteger.get() * 1.0 / taskCount * 100;
            double costSeconds = (System.currentTimeMillis() - start) / 1000.0d;
            log.info("TripType:{},Airline: {},  DATE:{}, Total: {}, SUCCESS:{}, SUCCESS-RATE:{}%, CostTime:{}second",
                    tripType, AIRLINE, DateUtil.formatDate(date),
                    taskCount, successInteger.get(),
                    new DecimalFormat("#0.00").format(rate),
                    String.format("%02f",costSeconds));

            lccSchedule.setSuccessCount(successInteger.get());
            scheduleService.endTask(lccSchedule);
        } catch (InterruptedException e) {
            scheduleService.stopTask(lccSchedule);
            log.error("j5task-thread-Interrupted:{}",e.getMessage());
        }
    }

    /**
     * 调度任务
     * @param tripType
     * @param logId
     * @param departureDate
     * @param airline
     * @param seq
     * @return
     */
    private boolean runDataTask(String tripType, long logId,
                                String departureDate, Lcc_Airline airline, int seq) {
        // 单程处理
        if (TripType.ONE_WAY.getCode().equals(tripType)) {
            LccResultDto resultDto = lccJ5GetDataService.getOneWayJ5TicketData(
                    airline.getDepCity(), airline.getArrCity(), departureDate, CURRENCY,seq);
            if (resultDto.isSuccess()) {
                List<LccResultDataDto> data = resultDto.getData();
                //log.debug("getTripTypeJ5TicketData-success:dep:{},arr:{},depData:{}",
                      //  airline.getDepCity(), airline.getArrCity(), departureDate);
                // 非空数据执行保存操作，执行成功有可能返回数据为空
                if (null != data && !data.isEmpty()) {
                    lccJ5SaveDataService.saveOneWay(data, logId);
                }
               return true;
            }
            //往返处理
        } else if (TripType.ROUND_TRIP.getCode().equals(tripType)) {
            //未确定返程日期
        }
        return false;
    }

    /**
     * 获取航线
     *
     * @return
     */
    private List<Lcc_Airline> getAirlineList() {
        QueryWrapper<Lcc_Airline> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT depCity,arrCity")
                .eq("hangSi", AIRLINE.getCode())
                .orderByAsc("depCity");
        List<Lcc_Airline> lccAirlineList = lccAirlineService.list(queryWrapper);
        return lccAirlineList;
    }



    public static void main(String[] args) {
        try {
            /*StringBuilder content = new StringBuilder();
            try (BufferedReader br = new BufferedReader(new FileReader("D:/json.txt"))) {
                String line;
                while ((line = br.readLine()) != null) {
                    content.append(line);
                }
            }
            JSONObject result = JSON.parseObject(content.toString());
            J5DataVo j5DataVo =  result.getJSONObject("data").toJavaObject(J5DataVo.class);
            new Lcc_J5GetDataServiceImpl().saveData(j5DataVo);*/
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
